<template>
    <view class="container">
        <!-- 顶部整体区域（轮播图+导航+搜索） -->
        <view class="header-section">
            <!-- 轮播图作为背景 -->
            <view class="swiper-container">
                <swiper class="swiper" circular :indicator-dots="true" :autoplay="true" :interval="3000"
                    :duration="500">
                    <swiper-item v-for="(item, index) in bannerList" :key="index">
                        <image :src="item.imageUrl" mode="aspectFill" class="swiper-image" />
                    </swiper-item>
                </swiper>
            </view>

            <!-- 顶部导航栏和搜索栏浮在轮播图上 -->
            <view class="header-content">
                <!-- 顶部导航栏 -->
                <view class="header">
                    <image :src="getAssetUrl('/logo.svg')" class="logo" mode="aspectFit">
                    </image>
                </view>

                <!-- 搜索栏 -->
                <view class="search-box">
                    <view class="search-input" @click="navigateToSearch">
                        <uni-icons type="search" size="18" color="#999"></uni-icons>
                        <input type="text" placeholder="债券/发行人/资讯" placeholder-class="placeholder" disabled />
                    </view>
                </view>
            </view>
        </view>

        <!-- 成都城投概览卡片 -->
        <view class="overview-card" v-if="hasBondPanelPermission">
            <!-- 成都城投概览区域 -->
            <view class="overview-section">
                <view class="overview-header">
                    <image :src="getAssetUrl('/home/<USER>')" class="icon-bank" mode="">
                    </image>
                    <text class="overview-title">{{ getUserInfo?.companyName || '公司' }}({{ version === 'group_mp' ? '债券概览' : '债券概览' }})</text>
                </view>

                <view class="overview-grid">
                    <view class="overview-item">
                        <text class="item-label">存续期债券(只)</text>
                        <text class="item-value number-font">{{ cdctGroupOverview?.num || 0 }}</text>
                    </view>
                    <view class="overview-item">
                        <text class="item-label">债券余额(亿)</text>
                        <text class="item-value number-font">{{ cdctGroupOverview?.balance || 0 }}</text>
                    </view>
                    <view class="overview-item">
                        <text class="item-label">票面加权(%)</text>
                        <view class="item-value-wrapper">
                            <text class="item-value number-font">{{
                                formatToFourDecimals(cdctGroupOverview?.rightCouponrate) }}</text>
                        </view>
                    </view>
                    <view class="overview-item">
                        <text class="item-label">行权久期/到期久期</text>
                        <view class="item-value-wrapper">
                            <text class="item-value number-font">{{ cdctGroupOverview?.exerciseDuration }}/{{
                                cdctGroupOverview?.maturityDuration }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 债券类型分析卡片 -->
        <view class="analysis-card" v-if="hasBondPanelPermission">
            <!-- 债券类型分析区域 -->
            <view class="analysis-section">
                <view class="analysis-header">
                    <view class="title-icon"></view>
                    <text class="title-text">债券类型分析</text>
                </view>

                <!-- 债券金额总计 -->
                <view class="bond-total-amount">
                    <text class="amount-label">项目金额(亿元):</text>
                    <text class="amount-value number-font">{{ totalBondAmount }}</text>
                </view>

                <!-- 单位提示 -->
                <view class="unit-tip">
                    <text class="unit-text">单位: 亿</text>
                </view>

                <!-- 使用封装的债券分析图表组件 -->
                <bond-analysis-chart :chart-data="bondAnalysisData"></bond-analysis-chart>
            </view>
        </view>

        <!-- 集团债券卡片 - 使用组件 -->
        <view class="bond-list-wrapper" v-if="showBondListCard">
            <bond-list-card :title="bondListCardTitle" :header-items="bondHeaderItems" :bond-list="bondList"
                :show-more-link="true" :field-names="bondFieldNames" moreUrl="/subPageA/my-bonds/my-bonds"
                @load-more="handleBondListLoadMore">
            </bond-list-card>
        </view>

        <!-- 我的应用卡片 -->
        <view class="my-apps-card" ref="myAppsCard" v-if="visibleApps.length > 0">
            <view class="apps-header">
                <view class="title-icon"></view>
                <text class="title-text">我的应用</text>
            </view>

            <view class="apps-grid">
                <view class="app-item" v-for="(app, index) in visibleApps" :key="index" @click="navigateTo(app.path)">
                    <view class="app-icon">
                        <image class="app-icon-inner" :src="app.icon" mode="aspectFit"></image>
                    </view>
                    <text class="app-name">{{ app.name }}</text>
                </view>
            </view>
        </view>

        <!-- 研究与资讯卡片 -->
        <view class="research-card" ref="researchCard" v-if="showResearchCard">
            <!-- 标签页 -->
            <view class="tab-header">
                <view class="tab-item" v-if="hasResearchTopicPermission" :class="{ active: activeTab === 'research' }"
                    @click="switchTab('research')">
                    <text class="tab-text">专题研究</text>
                    <view class="tab-line" v-show="activeTab === 'research'"></view>
                </view>
                <view class="tab-item" v-if="hasMarketNewsPermission" :class="{ active: activeTab === 'news' }"
                    @click="switchTab('news')">
                    <text class="tab-text">债市资讯</text>
                    <view class="tab-line" v-show="activeTab === 'news'"></view>
                </view>
            </view>

            <!-- 时间线列表 -->
            <view class="timeline-list" :class="{ expanded: isResearchExpanded }">
                <!-- 有数据时显示时间线 -->
                <view v-if="currentTabData.length > 0" class="timeline-item" v-for="(report, index) in visibleReports"
                    :key="index">
                    <!-- 时间线 -->
                    <view class="timeline-point"></view>
                    <view class="timeline-line" v-if="index < visibleReports.length - 1"></view>

                    <!-- 报告内容 -->
                    <view class="report-content" :class="{ 'report_active': index === 0 }">
                        <!-- 报告标签和日期 -->
                        <view class="report-header">
                            <view class="report-labels">
                                <text class="new-tag" v-if="index === 0">NEW</text>
                                <text class="report-type">{{ report.department }}</text>
                            </view>
                            <text class="report-date number-font">{{ report.date }}</text>
                        </view>

                        <!-- 报告标题 -->
                        <text class="report-title">{{ report.title }}</text>

                        <!-- 报告部门 -->
                        <text class="report-dept">{{ report.source }}</text>
                    </view>
                </view>

                <!-- 无数据时显示缺省状态 -->
                <view v-else class="no-data-placeholder">
                    <text class="no-data-text">暂无{{ activeTab === 'research' ? '专题研究' : '债市资讯' }}数据</text>
                </view>
            </view>

            <!-- 查看更多（只有数据大于3条时才显示） -->
            <view v-if="shouldShowMoreButton" class="view-more-reports" @click="showMoreReports">
                <image class="icon-arrow" :class="{ 'rotate-180': isResearchExpanded }"
                    :src="getAssetUrl('/home/<USER>')" mode=""></image>
                <text class="more-reports-text">{{ isResearchExpanded ? '收起' : '查看更多' }}</text>
            </view>
        </view>

        <!-- 自定义tabBar -->
        <AppTabBar :selectNumber="0" :permissionData="getTabBarPermissions" />
    </view>
</template>

<script setup lang="ts">
// ==================== 导入依赖 ====================
import { reactive, ref, onMounted, watch, computed, onUnmounted } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { getSql, getMarketConsultation, getBondOverview, getBondTypeAnalysis } from '@/api/common';
import BondListCard from '@/components/common/BondListCard.vue';
import AppTabBar from '@/components/AppTabBar/index.vue';
import BondAnalysisChart from '@/components/BondAnalysisChart/index.vue';
import { usePermissionStore } from '@/stores/permission';
import { storeToRefs } from 'pinia';
import { getAssetUrl } from '@/config/assets';

// ==================== 状态管理 ====================
const permissionStore = usePermissionStore();
const {
    getUserInfo,
    getTabBarPermissions,
    getSysVersion,
    // 权限检查相关
    hasBondPanelPermission,
    hasResearchTopicPermission,
    hasMarketNewsPermission,
    showResearchCard,
    showBondListCard,
    bondListCardTitle,
    getVisibleApps
} = storeToRefs(permissionStore);

// ==================== 工具方法 ====================
// 格式化数值到小数点后四位
const formatToFourDecimals = (value: number | string | null | undefined): string => {
    if (value === null || value === undefined || value === '') {
        return '0.0000';
    }
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0.0000' : numValue.toFixed(4);
};

// ==================== 系统配置 ====================
const version = computed(() => getSysVersion.value || 'company_mp');

// 监听版本变化，更新相关数据
watch(version, (newVal) => {
    // 重新获取所有相关数据
    fetchGroupOverview();
    fetchBondList();
    fetchMarketConsultationData();
});

// ==================== 用户数据 ====================
const outCompCode = computed(() => getUserInfo.value?.outCompCode || '');

// ==================== 轮播图配置 ====================
const bannerList = ref([
    { imageUrl: getAssetUrl('/banner.png'), id: 1 },
    { imageUrl: getAssetUrl('/banner.png'), id: 2 },
    { imageUrl: getAssetUrl('/banner.png'), id: 3 }
]);

// ==================== 集团概览数据 ====================
const cdctGroupOverview = ref({
    num: 0,
    balance: 0,
    rightCouponrate: 0,
    exerciseDuration: 0,
    maturityDuration: 0
});

// 获取集团概览数据
const fetchGroupOverview = () => {
    getBondOverview({}).then(res => {
        cdctGroupOverview.value = res.data.data[0];
    });
};

// ==================== 债券列表数据 ====================


const bondList = ref([]);

// 债券表头配置
const bondHeaderItems = ['债券简称', '最新估值(%)', '票面利率(%)'];

// 字段名映射配置
const bondFieldNames = ref({
    name: 'sInfoName',         // 债券名称字段
    rate: 'valuation',         // 最新估值字段
    change: 'latestCouponrate', // 票面利率字段
    changeType: 'changeType'    // 变化类型字段，用于样式
});

// 获取债券列表数据
const fetchBondList = () => {
    const params = {
        params: {
            ownedModuleid: "708631605142536192",
            ccid: "e6193b03124c43a7933385c5dc80a98c"
        },
        page: {
            pageNo: 1,
            pageSize: 50
        }
    };

    getSql(params).then(res => {
        const list = res?.data?.data?.pageInfo?.list || [];

        // 处理列表数据，添加changeType字段并处理null值
        const processedList = list.map((item) => {
            const valuation = item.valuation === null ? '--' : item.valuation;
            const latestCouponrate = item.latestCouponrate === null ? '--' : item.latestCouponrate;

            // 根据值的正负来设置样式类
            const changeType = latestCouponrate < 0 ? 'rate-down' : 'rate-up';

            return {
                ...item,
                valuation,
                latestCouponrate,
                changeType
            };
        });

        bondList.value = processedList;
    }).catch(err => {
        // 获取债券列表数据失败处理
    });
};

// 债券列表加载更多处理
const handleBondListLoadMore = () => {
    // 暂时保留接口，后续可扩展
};

// ==================== 债券类型分析数据 ====================
const bondTypeAnalysis = ref([]);

// 预定义颜色配置，用于不同债券类型
const bondTypeColors = [
    { start: '#99B0FF', end: '#6D89FD' },
    { start: '#A5EA8C', end: '#7BD659' },
    { start: '#FFDD95', end: '#FFCA59' },
    { start: '#FFBAB2', end: '#FF9385' },
    { start: '#E2C6FF', end: '#CAA5F5' },
    { start: '#FFC07F', end: '#FF9033' },
    { start: '#B5F2F2', end: '#7DDFDF' },
    { start: '#FFAFD1', end: '#FF80B3' },
    { start: '#C1E6A2', end: '#A0D573' },
    { start: '#D2C1FF', end: '#B59FFF' },
    { start: '#99B0FF', end: '#6D89FD' },
    { start: '#A5EA8C', end: '#7BD659' },
    { start: '#FFDD95', end: '#FFCA59' },
    { start: '#FFBAB2', end: '#FF9385' },
    { start: '#E2C6FF', end: '#CAA5F5' },
    { start: '#FFC07F', end: '#FF9033' }
];

// 债券分析数据类型定义
interface BondAnalysisItem {
    type: string;
    value: number;
    color: {
        start: string;
        end: string;
    };
}

// 响应式的债券分析数据
const bondAnalysisData = ref<BondAnalysisItem[]>([]);

// 计算债券总金额
const totalBondAmount = computed(() => {
    return bondAnalysisData.value.reduce((sum, item) => sum + (item.value || 0), 0).toFixed(4);
});

// 获取债券类型分析数据
const fetchBondTypeAnalysis = () => {
    getBondTypeAnalysis().then(res => {
        console.log(res,'res')
        bondTypeAnalysis.value = res.data.data;
        
        // 将接口数据转换为图表组件需要的格式
        bondAnalysisData.value = res.data.data.map((item, index) => ({
            type: item.bondTypeName2,
            value: item.amtsum,
            color: bondTypeColors[index % bondTypeColors.length] // 循环使用颜色配置
        }));
    });
};

// ==================== 我的应用配置 ====================
const myApps = reactive([
    { name: '市场利率', iconBg: 'bg-yellow', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/market-rate/market-rate' },
    { name: '政策利率', iconBg: 'bg-purple', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/policy-rate/policy-rate' },
    { name: '债券收益率', iconBg: 'bg-blue', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/bond-yield/bond-yield' },
    { name: '对标分析', iconBg: 'bg-green', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/benchmark-analysis/benchmark-analysis' },
    { name: '信用利差', iconBg: 'bg-yellow', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/credit-spread/credit-spread' },
    { name: '发行定价', iconBg: 'bg-blue', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/issuance-pricing/issuance-pricing' },
    { name: '注册进度', iconBg: 'bg-yellow', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/register-progress/register-progress' },
    { name: '注册额度', iconBg: 'bg-green', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/register-quota/register-quota' },
    { name: '二级成交', iconBg: 'bg-blue', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/transaction-completion/transaction-completion' },
    { name: '债市日历', iconBg: 'bg-purple', icon: getAssetUrl('/home/<USER>'), path: '/subPageA/bond-calendar/bond-calendar' }
]);

// 计算显示的应用列表 - 直接使用store中的方法
const visibleApps = computed(() => getVisibleApps.value(myApps));

// ==================== 研究与资讯模块 ====================
const defaultActiveTab = computed(() => {
    if (hasResearchTopicPermission.value) {
        return 'research';
    } else if (hasMarketNewsPermission.value) {
        return 'news';
    }
    return 'research';
});

const activeTab = ref('research'); // 当前激活的标签页
const isResearchExpanded = ref(false); // 展开状态
const marketNewsData = ref<any[]>([]); // 债市资讯数据
const researchTopicData = ref<any[]>([]); // 专题研究数据

// 监听权限变化，确保当前选中的tab是有权限的
watch([hasResearchTopicPermission, hasMarketNewsPermission], () => {
    // 如果当前选中的tab没有权限，自动切换到有权限的tab
    if (activeTab.value === 'research' && !hasResearchTopicPermission.value) {
        if (hasMarketNewsPermission.value) {
            activeTab.value = 'news';
        }
    } else if (activeTab.value === 'news' && !hasMarketNewsPermission.value) {
        if (hasResearchTopicPermission.value) {
            activeTab.value = 'research';
        }
    }
}, { immediate: true });

// 切换标签页
const switchTab = (tab: string) => {
    // 检查权限再切换
    if (tab === 'research' && hasResearchTopicPermission.value) {
        activeTab.value = tab;
    } else if (tab === 'news' && hasMarketNewsPermission.value) {
        activeTab.value = tab;
    }
};

// 展开/收起逻辑
const showMoreReports = () => {
    isResearchExpanded.value = !isResearchExpanded.value;
};

// 计算需要显示的报告（考虑权限）
const visibleReports = computed(() => {
    let data: any[] = [];
    if (activeTab.value === 'research' && hasResearchTopicPermission.value) {
        data = researchTopicData.value;
    } else if (activeTab.value === 'news' && hasMarketNewsPermission.value) {
        data = marketNewsData.value;
    }
    return isResearchExpanded.value ? data : data.slice(0, 5);
});

// 计算当前标签页的数据总量（考虑权限）
const currentTabData = computed(() => {
    if (activeTab.value === 'research' && hasResearchTopicPermission.value) {
        return researchTopicData.value;
    } else if (activeTab.value === 'news' && hasMarketNewsPermission.value) {
        return marketNewsData.value;
    }
    return [] as any[];
});

// 判断是否显示查看更多按钮（数据大于3条才显示）
const shouldShowMoreButton = computed(() => {
    return currentTabData.value.length > 3;
});

// 获取债市咨询和专题研究数据
const fetchMarketConsultationData = () => {
    getMarketConsultation().then(res => {
        if (res?.data?.data) {
            const { data } = res.data;

            // 处理债市资讯数据
            const marketInfo = data.marketInfo || data.债市资讯;
            if (marketInfo) {
                marketNewsData.value = marketInfo.map(item => ({
                    date: item.publishDate,
                    title: item.title,
                    department: item.category,
                    source: item.source || '暂无来源'
                }));
            }

            // 处理专题研究数据
            const researchInfo = data.researchTopic || data.专题研究;
            if (researchInfo) {
                researchTopicData.value = researchInfo.map(item => ({
                    date: item.publishDate,
                    title: item.title,
                    department: item.category,
                    source: item.source || '暂无来源'
                }));
            }
        }
    }).catch(err => {
        // 获取债市咨询和专题研究数据失败处理
    });
};

// ==================== 导航方法 ====================
// 通用导航方法
const navigateTo = (path: string) => {
    uni.navigateTo({ url: path });
};

// 跳转到搜索页面
const navigateToSearch = () => {
    uni.navigateTo({ url: '/subPageB/search-page/index' });
};


// 监听版本切换事件
onMounted(() => {
    // 监听来自版本切换的权限更新事件
    uni.$on('versionChanged', async (data) => {
        // 版本切换后，重新获取页面数据
        fetchBondTypeAnalysis()

        fetchGroupOverview();
        fetchBondList();
        fetchMarketConsultationData();
    });
});

// 组件卸载时移除事件监听
onUnmounted(() => {
    uni.$off('versionChanged');
});

// 组件挂载时初始化
onMounted(() => {
    // 初始化数据
    fetchBondTypeAnalysis()
    fetchGroupOverview();
    fetchMarketConsultationData();
    fetchBondList();
});
</script>

<style>

/* page {
    overflow-y: auto;
    background-color: #F4F5F9;
} */

/* 隐藏滚动条 - WebKit浏览器 */
::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
}

.uni-scroll-view::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
}

.icon-arrow {
    width: 32rpx;
    height: 32rpx;
}

.uni-searchbar__box {
    height: 80rpx !important;
    /* background: none!important; */
}

.uni-searchbar__box-icon-search {
    transform: scale(1.2);
    padding-left: 30rpx !important;
    background-color: none !important;
}

/*
.load-trigger {
    height: 1px;
    width: 100%;
    opacity: 0;
    position: relative;
    top: -300rpx;
}
*/
</style>

<style lang="scss" scoped>
.container {
    height: calc(100vh - 100rpx - env(safe-area-inset-bottom));
    background-color: #F4F5F9;
    position: relative;
    width: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 0;
    /* 不需要额外的底部padding，已经计算在高度中 */

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }

    // 顶部整体区域
    .header-section {
        position: relative;
        width: 100%;
        height: 600rpx; // 根据需要调整高度
        margin-bottom: 30rpx;
        overflow: hidden;

        // 轮播图容器
        .swiper-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;

            .swiper {
                width: 100%;
                height: 100%;

                :deep(.uni-swiper-dots) {
                    bottom: 30rpx;
                    z-index: 10;
                }

                :deep(.uni-swiper-dot) {
                    width: 12rpx;
                    height: 12rpx;
                    border-radius: 6rpx;
                    background: rgba(255, 255, 255, 0.5);
                    transition: all 0.3s;

                    &.uni-swiper-dot-active {
                        width: 24rpx;
                        background: #FFFFFF;
                    }
                }

                .swiper-image {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        // 顶部内容层（导航栏和搜索栏）
        .header-content {
            position: relative;
            z-index: 2;
            padding: 0 20rpx;
        }
    }

    // 通用卡片样式
    .common-card {
        margin: 20rpx;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
        overflow: hidden;
        padding: 30rpx;
    }

    // 顶部导航栏
    .header {
        box-sizing: border-box;
        margin-top: 95rpx;
        display: flex;
        align-items: center;

        .logo {
            width: 240rpx;
            height: 80rpx;
            filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
        }
    }

    /* 搜索框 */
    .search-box {
        display: flex;
        align-items: center;
        margin-top: 30rpx;
        margin-bottom: 20rpx;

        .search-input {
            flex: 1;
            height: 72rpx;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 36rpx;
            display: flex;
            align-items: center;
            padding: 0 24rpx;
            backdrop-filter: blur(10rpx);
            box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);

            uni-icons {
                margin-right: 10rpx;
            }

            input {
                flex: 1;
                height: 100%;
                font-size: 28rpx;
            }

            .placeholder {
                color: #999999;
            }
        }
    }

    // 成都城投概览卡片
    .overview-card {
        @extend .common-card;
        background: #FFFFFF;
        padding-bottom: 10rpx;

        // 成都城投概览区域
        .overview-section {
            .overview-header {
                display: flex;
                align-items: center;
                margin-bottom: 30rpx;

                .icon-bank {
                    width: 60rpx;
                    height: 60rpx;
                    margin-right: 16rpx;
                }

                .overview-title {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                }
            }

            .overview-grid {
                display: flex;
                justify-content: space-between;
                box-sizing: border-box;
                flex-wrap: wrap;

                .overview-item {
                    background-color: #FAFAFA;
                    width: 43%;
                    padding: 30rpx 20rpx;
                    border-radius: 16rpx;
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 20rpx;

                    .item-label {
                        font-size: 24rpx;
                        color: #838383;
                        margin-bottom: 16rpx;
                    }

                    .item-value {
                        font-family: 'iconfont';
                        font-size: 30rpx;
                        color: #000000;

                        &.highlight {
                            color: #FF8E2B;
                        }
                    }

                    .item-value-wrapper {
                        display: flex;
                        align-items: center;

                        .icon-arrow {
                            color: #FF8E2B;
                            margin-left: 10rpx;
                        }

                        .icon-info {
                            color: #999;
                            margin-left: 10rpx;
                        }
                    }
                }
            }
        }
    }

    // 债券类型分析卡片
    .analysis-card {
        @extend .common-card;
        background-color: #fff;

        // 债券类型分析区域
        .analysis-section {
            .analysis-header {
                display: flex;
                align-items: center;
                margin-bottom: 40rpx;
                margin-left: 10rpx;

                .title-icon {
                    width: 48rpx;
                    height: 52rpx;
                    position: relative;
                    top: -10rpx;

                    &::before {
                        content: '';
                        position: absolute;
                        inset: 0;
                        background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
                        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                    }
                }

                .title-text {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                    transform: translateX(-25rpx);
                }
            }

            // 债券金额总计
            .bond-total-amount {
                display: flex;
                align-items: baseline;
                margin-bottom: 20rpx;

                .amount-label {
                    font-size: 28rpx;
                    color: #838383;
                    margin-right: 10rpx;
                }

                .amount-value {
                    font-size: 38rpx;
                    color: #FF8E2B; // 橙色
                }
            }

            // 单位提示
            .unit-tip {
                display: flex;
                align-items: center;
                margin-bottom: 20rpx;

                .unit-text {
                    font-size: 28rpx;
                    color: #999;
                }
            }
        }
    }

    // 集团债券卡片包装器
    .bond-list-wrapper {
        margin: 20rpx;
    }

    // 集团债券卡片
    .bond-list-card {
        @extend .common-card;
        background-color: #fff;

        // 卡片标题
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 40rpx;

            .bond-title {
                display: flex;
                align-items: center;

                .title-icon {
                    width: 48rpx;
                    height: 52rpx;
                    position: relative;
                    top: -10rpx;

                    &::before {
                        content: '';
                        position: absolute;
                        inset: 0;
                        background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
                        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                    }
                }

                .title-text {
                    font-size: 32rpx;
                    font-weight: bold;
                    color: #333;
                    transform: translateX(-25rpx);
                }
            }

            .more-link {
                display: flex;
                align-items: center;
                color: #FF8E2B;

                .more-text {
                    font-size: 28rpx;
                    margin-right: 10rpx;
                }

                .icon-right {
                    width: 28rpx;
                    height: 28rpx;
                }
            }
        }

        // 债券列表
        .bond-list {
            padding: 0 30rpx;

            .list-header {
                display: flex;
                justify-content: space-between;
                padding: 30rpx;
                background: linear-gradient(180deg, #FAFAFA 0%, #F4F4F4 100%);
                box-shadow: inset 0rpx -2rpx 0rpx 0rpx #EAE9E9;

                .header-item {
                    font-size: 28rpx;
                    color: #000;
                    font-weight: 500;
                }
            }

            .list-content {
                max-height: 500rpx;
                overflow: hidden;
                transition: max-height 0.3s ease;

                &.expanded {
                    max-height: 1200rpx;
                    overflow-y: auto;
                }

                /* 自定义滚动条样式 */
                &.expanded::-webkit-scrollbar {
                    width: 6rpx;
                }

                &.expanded::-webkit-scrollbar-thumb {
                    background-color: #e0e0e0;
                    border-radius: 3rpx;
                }

                &.expanded::-webkit-scrollbar-track {
                    background-color: #f5f5f5;
                }

                .list-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 30rpx;
                    border-bottom: 2rpx solid #EAE9E9;

                    .bond-name {
                        font-size: 28rpx;
                        color: #333;
                    }

                    .bond-rate-container {
                        display: flex;
                        align-items: center;

                        .bond-rate {
                            font-size: 28rpx;
                            color: #333;
                            margin-right: 20rpx;
                        }

                        .bond-change {
                            font-size: 28rpx;
                            margin-right: 10rpx;

                            &.rate-up {
                                color: #67c23a;
                            }

                            &.rate-down {
                                color: #f56c6c;
                            }
                        }

                        .icon-arrow {
                            width: 32rpx;
                            height: 32rpx;
                        }
                    }
                }
            }

            // 查看更多
            .view-more {
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;
                padding: 30rpx;
                cursor: pointer;

                .more-text {
                    font-size: 28rpx;
                    color: #666;
                }

                .icon-arrow {
                    width: 24rpx;
                    height: 26rpx;
                    margin-bottom: 10rpx;
                }
            }
        }
    }

    // 我的应用卡片
    .my-apps-card {
        @extend .common-card;
        background-color: #fff;

        // 应用标题
        .apps-header {
            display: flex;
            align-items: center;
            margin-bottom: 20rpx;
            margin-left: 10rpx;
            margin-top: 10rpx;

            .title-icon {
                width: 48rpx;
                height: 52rpx;
                position: relative;
                top: -10rpx;

                &::before {
                    content: '';
                    position: absolute;
                    inset: 0;
                    background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
                    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                }
            }

            .title-text {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
                transform: translateX(-25rpx);
            }
        }

        // 应用网格
        .apps-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            // gap: 40rpx;
            // padding: 0 20rpx 30rpx;

            // 单个应用
            .app-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: flex-start;
                margin-bottom: 20rpx;

                .app-icon {
                    width: 92rpx;
                    height: 90rpx;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    // margin-bottom: 16rpx;

                    &.bg-yellow {
                        background-color: #f8d785;
                    }

                    &.bg-purple {
                        background-color: #c7b6f2;
                    }

                    &.bg-blue {
                        background-color: #a4caff;
                    }

                    &.bg-green {
                        background-color: #a8e8c2;
                    }

                    .app-icon-inner {
                        width: 80rpx;
                        height: 80rpx;
                    }
                }

                .app-name {
                    font-size: 24rpx;
                    color: #000;
                    text-align: center;
                }
            }
        }
    }

    // 研究与资讯卡片
    .research-card {
        @extend .common-card;
        background-color: #fff;
        padding: 0;

        // 标签页标题
        .tab-header {
            display: flex;
            // border-bottom: 2rpx solid #f0f0f0;

            .tab-item {
                position: relative;
                padding: 30rpx 40rpx;
                cursor: pointer;

                .tab-text {
                    font-size: 32rpx;
                    color: #333;
                    font-weight: 600;
                }

                .tab-line {
                    position: absolute;
                    bottom: -2rpx;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 40rpx;
                    height: 6rpx;
                    background-color: #FF8E2B;
                    border-radius: 20rpx;
                    transition: all 0.3s ease;
                }

                &.active {
                    .tab-text {
                        color: #FF8E2B;
                    }
                }

            }
        }

        // 时间线列表
        .timeline-list {
            padding: 30rpx;
            position: relative;
            max-height: 800rpx;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .timeline-list.expanded {
            max-height: 1200rpx;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .timeline-list.expanded::-webkit-scrollbar {
            display: none;
        }

        // 无数据缺省状态
        .no-data-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 80rpx 0;


            .no-data-text {
                font-size: 28rpx;
                color: #999;
                text-align: center;
            }
        }

        // 时间线项目
        .timeline-item {
            position: relative;
            padding-left: 40rpx;
            margin-bottom: 30rpx;

            // 时间线点和线
            .timeline-point {
                position: absolute;
                left: 0;
                top: 10rpx;
                width: 20rpx;
                height: 20rpx;
                border-radius: 50%;
                background: linear-gradient(180deg, #FFC156 0%, #FF8E2B 100%);
                z-index: 2;
            }

            .timeline-line {
                position: absolute;
                left: 8rpx;
                top: 30rpx;
                width: 4rpx;
                height: calc(100% + 10rpx);
                background-color: transparent;
                border-left: 4rpx dashed #eaeaea;
            }

            // 报告内容
            .report-content {
                background-color: #f9f9f9;
                border: 2px solid #FFFFFF;
                border-radius: 16rpx;
                padding: 30rpx;
                position: relative;
                overflow: hidden;
                box-shadow: 0rpx 20rpx 40rpx 0rpx rgba(0, 0, 0, 0.03);

                // 报告标签和日期
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16rpx;
                    margin-top: 10rpx;

                    .report-labels {
                        display: flex;
                        align-items: center;

                        .new-tag {
                            padding: 4rpx 16rpx;
                            background-color: #b794f6;
                            color: white;
                            font-size: 20rpx;
                            margin-right: 16rpx;
                            position: absolute;
                            left: 0;
                            top: 0;
                            border-bottom-right-radius: 24rpx;
                        }

                        .report-type {
                            font-size: 28rpx;
                            color: #666;
                        }
                    }

                    .report-date {
                        font-size: 26rpx;
                        color: #999;
                    }
                }

                // 报告标题
                .report-title {
                    font-size: 30rpx;
                    color: #000;
                    display: block;
                    text-align: justify;
                    line-height: 1.6;
                    margin-bottom: 24rpx;
                }

                // 报告部门
                .report-dept {
                    font-size: 26rpx;
                    color: rgba(0, 0, 0, 0.6);
                }

            }

            .report_active {
                background-color: #F6F5FF !important;
            }
        }

        // 查看更多
        .view-more-reports {
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            padding: 30rpx;
            cursor: pointer;

            .more-reports-text {
                font-size: 28rpx;
                color: #666;
            }

            .icon-arrow {
                margin-bottom: 10rpx;
                width: 24rpx;
                height: 26rpx;
                transition: transform 0.3s ease;
            }
        }
    }
}

// 图例颜色类
.color-blue {
    background-color: #6c8fd7;
}

.color-green {
    background-color: #5ebfab;
}

.color-yellow {
    background-color: #e9be67;
}

.color-purple {
    background-color: #a673bc;
}

// 图标字体通用样式

.icon-more,
.icon-scan {
    font-size: 48rpx;
    color: #333;
}

.rotate-180 {
    transform: rotate(180deg);
}

.view-more {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 30rpx;
    cursor: pointer;
}

.list-content {
    max-height: 500rpx;
    overflow: hidden;
    transition: max-height 0.3s ease;

    &.expanded {
        max-height: 1200rpx;
        overflow-y: auto;
        scrollbar-width: none;
        /* Firefox */
        -ms-overflow-style: none;
        /* IE and Edge */
    }
}

.list-content.expanded::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
}



// 图例项样式
.legend-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 6rpx 5rpx; // 减少内边距
    border-radius: 4rpx;
    margin-bottom: 12rpx; // 减少底部边距


    .legend-item-left {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 2rpx; // 减少底部边距
    }

    .legend-color {
        min-width: 18rpx;
        width: 18rpx;
        height: 18rpx;
        border-radius: 4rpx;
        margin-right: 15rpx;
        transition: opacity 0.3s;
        flex-shrink: 0;
    }

    .legend-label {
        font-size: 30rpx;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .legend-value {
        font-size: 30rpx;
        font-weight: bold;
        color: rgba(0, 0, 0, 1);
        text-align: left;
        padding-left: 40rpx;
        margin-top: 10rpx;
    }
}

.text-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>