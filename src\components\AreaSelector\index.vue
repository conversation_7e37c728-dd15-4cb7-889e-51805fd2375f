<template>
    <uni-popup ref="areaPopup" type="bottom" :safe-area="false" @change="onPopupChange">
        <view class="popup-content">
            <view class="popup-header">
                <text class="popup-title">区域选择</text>
                <view class="close-icon" @tap="closeAreaPicker">
                    <uni-icons type="close" size="20" color="#333"></uni-icons>
                </view>
            </view>

            <view class="popup-options">
                <view class="options-header">
                    <text>省市区多选</text>
                    <view class="options-actions">
                        <text @tap="selectAll">全选</text>
                        <text @tap="clearAll">清空</text>
                        <text @tap="invertAll">反选</text>
                    </view>
                </view>

                <view class="area-columns">
                    <!-- 省份列 -->
                    <view class="column">
                        <view class="column-header">省份</view>
                        <scroll-view class="column-content" scroll-y :show-scrollbar="false">
                            <view v-for="(province, index) in provinceList" :key="index" class="column-item"
                                :class="{ active: selectedProvince === province.id }" @tap="selectProvince(province)">
                                <view class="checkbox"
                                    :class="{ checked: isProvinceSelected(province), indeterminate: isProvinceIndeterminate(province) }"
                                    @tap.stop="toggleProvinceSelection(province)">
                                    <uni-icons v-if="isProvinceSelected(province)" type="checkmarkempty" size="14"
                                        color="#fff"></uni-icons>
                                    <view v-else-if="isProvinceIndeterminate(province)" class="indeterminate-icon">
                                    </view>
                                </view>
                                <text class="area-label">{{ province.cname }}</text>
                            </view>
                        </scroll-view>
                    </view>

                    <!-- 城市列 -->
                    <view class="column">
                        <view class="column-header">城市</view>
                        <scroll-view class="column-content" scroll-y :show-scrollbar="false">

                            <view v-for="(city, index) in cityList" :key="index" class="column-item"
                                :class="{ active: selectedCity === city.id }" @tap="selectCity(city)">
                                <view class="checkbox"
                                    :class="{ checked: isCitySelected(city), indeterminate: isCityIndeterminate(city) }"
                                    @tap.stop="toggleCitySelection(city)">
                                    <uni-icons v-if="isCitySelected(city)" type="checkmarkempty" size="14"
                                        color="#fff"></uni-icons>
                                    <view v-else-if="isCityIndeterminate(city)" class="indeterminate-icon"></view>
                                </view>
                                <text class="area-label">{{ city.cname }}</text>
                            </view>
                        </scroll-view>
                    </view>

                    <!-- 区县列 -->
                    <view class="column">
                        <view class="column-header">区县</view>
                        <scroll-view class="column-content" scroll-y :show-scrollbar="false">

                            <view v-for="(district, index) in districtList" :key="index" class="column-item selectable"
                                @tap="toggleDistrictSelection(district)">
                                <view class="checkbox" :class="{ checked: selectedDistricts.includes(district.id) }">
                                    <uni-icons v-if="selectedDistricts.includes(district.id)" type="checkmarkempty"
                                        size="14" color="#fff"></uni-icons>
                                </view>
                                <text class="area-label">{{ district.cname }}</text>
                            </view>
                        </scroll-view>
                    </view>
                </view>
            </view>

            <view class="popup-footer">
                <button class="confirm-btn" @tap="confirmSelection">确认</button>
            </view>
        </view>
    </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch, onMounted } from 'vue';
import { getAssetUrl } from '@/config/assets';
// 定义区域数据类型
export interface AreaItem {
    id: string;
    cname: string;
    levell: string;
    pid: string | null;
    children?: AreaItem[];
}

// 组件属性定义
const props = defineProps({
    // 是否显示弹窗
    visible: {
        type: Boolean,
        default: false
    },
    // 默认选中的区域（区级pid数组）
    defaultSelected: {
        type: Array as () => string[],
        default: () => []
    }
});

// 定义组件事件
const emits = defineEmits(['update:visible', 'areaChange', 'close']);

// 弹窗引用
const areaPopup = ref();

// 原始数据
const rawAreaData = ref<AreaItem[]>([]);
const isLoading = ref(false);
const loadError = ref('');

// 选择状态
const selectedProvince = ref('');
const selectedCity = ref('');
const selectedDistricts = ref<string[]>([]);

// 省份列表 - 直接使用递归数据的根级数据
const provinceList = computed((): AreaItem[] => {
    return rawAreaData.value;
});

// 城市列表
const cityList = ref<AreaItem[]>([]);

// 区县列表
const districtList = ref<AreaItem[]>([]);

// 获取区域数据
const fetchAreaData = async () => {
    if (isLoading.value || rawAreaData.value.length > 0) {
        return; // 防止重复加载
    }

    isLoading.value = true;
    loadError.value = '';

    try {
        // 构建静态资源URL
        const url = getAssetUrl('/areaData.json');
        
        const response = await uni.request({
            url: url,
            method: 'GET',
            dataType: 'json'
        });

        if (response.statusCode === 200 && response.data) {
            rawAreaData.value = response.data as AreaItem[];
            
            // 初始化默认选择
            initializeDefaultSelection();
            
            // 使用传入的默认选择，不进行自动全选
            selectedDistricts.value = [...(props.defaultSelected as string[])];
        } else {
            throw new Error(`HTTP ${response.statusCode}: 获取区域数据失败`);
        }
    } catch (error: any) {
        loadError.value = error.message || '获取区域数据失败';
        console.error('获取区域数据失败:', error);
        
        // 可以在这里添加用户提示
        uni.showToast({
            title: '加载区域数据失败',
            icon: 'none'
        });
    } finally {
        isLoading.value = false;
    }
};

// 初始化默认展示
const initializeDefaultSelection = () => {
    const provinces = provinceList.value;
    if (provinces.length > 0) {
        // 设置第一个省份为选中状态（仅用于展示）
        const firstProvince = provinces[0];
        selectedProvince.value = firstProvince.id;
        cityList.value = firstProvince.children || [];

        if (cityList.value.length > 0) {
            // 设置第一个城市为选中状态（仅用于展示）
            const firstCity = cityList.value[0];
            selectedCity.value = firstCity.id;
            districtList.value = firstCity.children || [];
        }
    }
};

// 组件挂载时获取数据
onMounted(() => {
    fetchAreaData();
});

// 监听visible变化
watch(() => props.visible, (newValue) => {
    if (newValue) {
        areaPopup.value?.open();
        
        // 直接使用传入的默认选择，不再进行自动全选
        selectedDistricts.value = [...(props.defaultSelected as string[])];
        
        // 如果数据已加载，重新初始化展示
        if (rawAreaData.value.length > 0) {
            initializeDefaultSelection();
        } else {
            // 如果数据未加载，触发数据获取
            fetchAreaData();
        }
    } else {
        areaPopup.value?.close();
    }
});

// 弹窗状态变化
const onPopupChange = (e: any) => {
    if (e.show === false) {
        emits('update:visible', false);
        emits('close');
    }
};

// 关闭弹窗
const closeAreaPicker = () => {
    emits('update:visible', false);
};

// 选择省份
const selectProvince = (province: AreaItem) => {
    selectedProvince.value = province.id;
    // 获取该省份下的所有城市
    cityList.value = province.children || [];

    if (cityList.value.length > 0) {
        // 自动选择第一个城市并展示其区县列表
        const firstCity = cityList.value[0];
        selectedCity.value = firstCity.id;
        districtList.value = firstCity.children || [];
    } else {
        selectedCity.value = '';
        districtList.value = [];
    }
};

// 选择城市
const selectCity = (city: AreaItem) => {
    selectedCity.value = city.id;
    // 获取该城市下的所有区县
    districtList.value = city.children || [];
};

// 切换区县选择
const toggleDistrictSelection = (district: AreaItem) => {
    const index = selectedDistricts.value.indexOf(district.id);
    if (index > -1) {
        selectedDistricts.value.splice(index, 1);
    } else {
        selectedDistricts.value.push(district.id);
    }
};

// 获取指定节点下的所有最后一级数据（叶子节点）
const getLeafNodesUnderItem = (item: AreaItem): AreaItem[] => {
    const leafNodes: AreaItem[] = [];
    
    const findLeafNodes = (currentItem: AreaItem) => {
        if (!currentItem.children || currentItem.children.length === 0) {
            // 没有子节点，是叶子节点
            leafNodes.push(currentItem);
        } else {
            // 有子节点，继续递归查找
            currentItem.children.forEach(child => {
                findLeafNodes(child);
            });
        }
    };
    
    findLeafNodes(item);
    return leafNodes;
};

// 判断省份是否全选
const isProvinceSelected = (province: AreaItem): boolean => {
    const leafNodes = getLeafNodesUnderItem(province);
    return leafNodes.length > 0 && leafNodes.every(leaf => selectedDistricts.value.includes(leaf.id));
};

// 判断省份是否半选
const isProvinceIndeterminate = (province: AreaItem): boolean => {
    const leafNodes = getLeafNodesUnderItem(province);
    const selectedCount = leafNodes.filter(leaf => selectedDistricts.value.includes(leaf.id)).length;
    return selectedCount > 0 && selectedCount < leafNodes.length;
};

// 判断城市是否全选
const isCitySelected = (city: AreaItem): boolean => {
    const leafNodes = getLeafNodesUnderItem(city);
    return leafNodes.length > 0 && leafNodes.every(leaf => selectedDistricts.value.includes(leaf.id));
};

// 判断城市是否半选
const isCityIndeterminate = (city: AreaItem): boolean => {
    const leafNodes = getLeafNodesUnderItem(city);
    const selectedCount = leafNodes.filter(leaf => selectedDistricts.value.includes(leaf.id)).length;
    return selectedCount > 0 && selectedCount < leafNodes.length;
};

// 切换省份选择状态
const toggleProvinceSelection = (province: AreaItem) => {
    const leafNodes = getLeafNodesUnderItem(province);
    const isSelected = isProvinceSelected(province);

    if (isSelected) {
        // 取消选择所有下级最后一级节点
        selectedDistricts.value = selectedDistricts.value.filter(id =>
            !leafNodes.some(leaf => leaf.id === id)
        );
    } else {
        // 选择所有下级最后一级节点
        leafNodes.forEach(leaf => {
            if (!selectedDistricts.value.includes(leaf.id)) {
                selectedDistricts.value.push(leaf.id);
            }
        });
    }
};

// 切换城市选择状态
const toggleCitySelection = (city: AreaItem) => {
    const leafNodes = getLeafNodesUnderItem(city);
    const isSelected = isCitySelected(city);

    if (isSelected) {
        // 取消选择所有下级最后一级节点
        selectedDistricts.value = selectedDistricts.value.filter(id =>
            !leafNodes.some(leaf => leaf.id === id)
        );
    } else {
        // 选择所有下级最后一级节点
        leafNodes.forEach(leaf => {
            if (!selectedDistricts.value.includes(leaf.id)) {
                selectedDistricts.value.push(leaf.id);
            }
        });
    }
};

// 全选
const selectAll = () => {
    const allLeafNodes = getAllLeafNodes();
    selectedDistricts.value = allLeafNodes.map(leaf => leaf.id);
};

// 清空
const clearAll = () => {
    selectedDistricts.value = [];
};

// 反选
const invertAll = () => {
    const allLeafNodes = getAllLeafNodes();
    const allLeafIds = allLeafNodes.map(leaf => leaf.id);
    selectedDistricts.value = allLeafIds.filter(id => !selectedDistricts.value.includes(id));
};

// 获取所有最后一级的数据（叶子节点）
const getAllLeafNodes = (): AreaItem[] => {
    const leafNodes: AreaItem[] = [];
    
    const findLeafNodes = (items: AreaItem[]) => {
        items.forEach(item => {
            if (!item.children || item.children.length === 0) {
                // 没有子节点，是叶子节点
                leafNodes.push(item);
            } else {
                // 有子节点，继续递归查找
                findLeafNodes(item.children);
            }
        });
    };
    
    findLeafNodes(rawAreaData.value);
    return leafNodes;
};

// 确认选择
const confirmSelection = () => {
    // 获取所有最后一级的数据（叶子节点）
    const allLeafNodes = getAllLeafNodes();
    
    // 过滤出选中的最后一级节点
    const selectedLeafNodes = allLeafNodes.filter(leaf => 
        selectedDistricts.value.includes(leaf.id)
    );
    
    // 获取选中叶子节点的pid值（即节点自身的pid属性）
    const selectedLeafPids = selectedLeafNodes
        .map(leaf => leaf.pid)
        .filter(Boolean) // 过滤掉可能的null或undefined值
        .filter((pid, index, self) => self.indexOf(pid) === index) as string[]; // 去重
    
    // 输出选中的最后一级节点的pid数组
    emits('areaChange', selectedLeafPids);
    emits('update:visible', false);
};
</script>

<style lang="scss" scoped>
/* 弹窗样式 */
.popup-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 30rpx 30rpx 0;
    max-height: 70vh;
    display: flex;
    flex-direction: column;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #F5F5F5;

    .popup-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }

    .close-icon {
        padding: 10rpx;
    }
}

.popup-options {
    flex: 1;
    overflow: hidden;
    padding: 20rpx 0;

    .options-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;

        text {
            font-size: 26rpx;
            color: #999;
        }

        .options-actions {
            display: flex;
            gap: 30rpx;

            text {
                color: #FF9900;
                font-size: 26rpx;

                &:active {
                    opacity: 0.8;
                }
            }
        }
    }
}

/* 三列布局样式 */
.area-columns {
    display: flex;
    height: 400rpx;
    overflow: hidden;

    .column {
        flex: 1;
        display: flex;
        flex-direction: column;

        .column-header {
            padding: 20rpx;
            background-color: #f8f8f8;
            font-size: 26rpx;
            font-weight: bold;
            color: #333;
            text-align: center;
        }

        .column-content {
            flex: 1;
            overflow-y: auto;

            /* 隐藏滚动条 - 兼容各端 */
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE 10+ */

            &::-webkit-scrollbar {
                display: none; /* Chrome Safari */
                width: 0;
                height: 0;
                background: transparent;
            }

            .column-item {
                padding: 20rpx;
                font-size: 26rpx;
                color: #333;
                transition: all 0.3s;
                cursor: pointer;
                display: flex;
                align-items: center;

                .checkbox {
                    width: 28rpx;
                    height: 28rpx;
                    border-radius: 6rpx;
                    border: 2rpx solid #DCDFE6;
                    margin-right: 15rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s;
                    flex-shrink: 0;

                    &.checked {
                        background-color: #FF9900;
                        border-color: #FF9900;
                    }

                    &.indeterminate {
                        background-color: #FF9900;
                        border-color: #FF9900;
                    }

                    .indeterminate-icon {
                        width: 16rpx;
                        height: 3rpx;
                        background-color: #fff;
                        border-radius: 2rpx;
                    }
                }

                .area-label {
                    flex: 1;
                    word-break: break-all;
                }

                &.selectable {
                    &:active {
                        background-color: #f8f8f8;
                    }
                }
            }

            .empty-state {
                padding: 40rpx 20rpx;
                text-align: center;
                color: #999;
                font-size: 24rpx;
            }
        }
    }
}

.popup-footer {
    padding: 30rpx;
    border-top: 2rpx solid #F5F5F5;

    .confirm-btn {
        width: 100%;
        height: 88rpx;
        background: #FF9900;
        border-radius: 44rpx;
        color: #FFFFFF;
        font-size: 32rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;

        &:active {
            opacity: 0.9;
        }
    }
}
</style>