<template>
	<view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<!-- 固定的头部区域 -->
		<view class="fixed-header">
			<CustomHead title="主承排名" />
		</view>
		<view class="content-wrapper">
			<scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
				<!-- 搜索框 - 放在卡片外部 -->
				<view class="search-box">
					<view class="search-input">
						<uni-icons type="search" size="18" color="#999"></uni-icons>
						<input type="text" placeholder="可根据主承销商查询" v-model="searchValue" placeholder-class="placeholder" @confirm="handleSearch" />
					</view>
					<text class="cancel-btn">取消</text>
				</view>

				<!-- 内容大卡片 -->
				<view class="content-card">
					<!-- 筛选条件 -->
					<view class="filter-tabs">
						<view class="filter-tab" @click="showDatePicker">
							<text>发行时间</text>
							<text class="arrow">▼</text>
						</view>
						<view class="filter-tab" @click="openInstitutionPopup">
							<text>机构类型</text>
							<text class="arrow">▼</text>
						</view>
						<view class="filter-tab" @click="openBondTypePopup">
							<text>债券类型</text>
							<text class="arrow">▼</text>
						</view>
					</view>

					<!-- 总体数据 -->
					<view class="total-data">
						<view class="total-item">
							<text class="label">总承销额(亿元):</text>
							<text class="value highlight number-font">{{ totalData.amount }}</text>
						</view>
						<view class="total-item">
							<text class="label">总笔数:</text>
							<text class="value highlight number-font">{{ totalData.count }}</text>
						</view>
					</view>

					<!-- 公司列表 -->
					<view class="company-list">
						<!-- 使用v-for动态渲染公司列表 -->
						<view class="company-item" @click="viewDetail(item)" v-for="(item, index) in displayCompanyList" :key="index">
							<view class="rank"
								:class="{ 'rank-1': item.rank === 1, 'rank-2': item.rank === 2, 'rank-3': item.rank === 3 }">
								<text class="number-font">{{ item.rank }}</text>
							</view>
							<view class="company-info">
								<text class="company-name">{{ item.name.slice(0, 7) + '...' }}</text>
								<text class="deal-count">笔数: <text class="number-font">{{ item.count }}</text></text>
							</view>
							<view class="amount number-font">{{ item.amount }}</view>
							<view class="progress-container">
								<view class="progress-bar" :style="{ width: item.progress + '%' }"></view>
							</view>
						</view>

						<!-- 查看更多按钮 -->
						<!-- <view class="view-more" @click="viewMore">
							<uni-icons type="more-filled" size="24" color="#BEBEBE"></uni-icons>
							<text>查看更多</text>
						</view> -->
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 日期选择器弹窗 -->
		<DateRangePicker :visible="showDateRangePicker" @update:visible="(val) => showDateRangePicker = val"
			@dateRangeChange="onDateRangeChange" />

		<!-- 机构类型选择弹窗 - 使用uni-popup -->
		<uni-popup ref="institutionPopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">机构类型</text>
					<view class="close-icon" @click="closeInstitutionPopup">×</view>
				</view>
				<view class="popup-body">
					<view class="type-list">
						<view class="type-item" v-for="(item, index) in institutionTypeOptions" :key="index"
							:class="{ 'selected': tempSelectedTypes.includes(item.value) }"
							@click="toggleInstitutionType(item.value)">
							<text class="type-name">{{ item.label }}</text>
							<view class="checkbox" v-if="tempSelectedTypes.includes(item.value)">✓</view>
						</view>
					</view>
				</view>
				<view class="popup-footer">
					<view class="btn reset-btn" @click="resetInstitutionTypes">重置</view>
					<view class="btn confirm-btn" @click="confirmInstitutionTypes">确定</view>
				</view>
			</view>
		</uni-popup>

		<!-- 债券类型选择弹窗 - 使用uni-popup -->
		<uni-popup ref="bondTypePopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">债券类型</text>
					<view class="close-icon" @click="closeBondTypePopup">×</view>
				</view>
				<view class="popup-body">
					<view class="popup-tabs">
						<view v-for="(tab, index) in bondTypeTabs" :key="index" class="popup-tab"
							:class="{ active: currentBondTypeTab === tab.value }" @click="switchBondTypeTab(tab.value)">
							{{ tab.label }}
						</view>
					</view>

					<scroll-view class="popup-options" scroll-y>
						<view class="options-header">
							<text>默认勾选全部</text>
							<view class="options-actions">
								<text @click="selectAllBondTypeOptions">全选</text>
								<text @click="clearBondTypeOptions">清空</text>
								<text @click="invertBondTypeSelection">反选</text>
							</view>
						</view>

						<view class="options-list">
							<view v-for="(option, index) in currentBondTypeOptions" :key="index" class="option-item"
								@click="toggleBondTypeOption(option)">
								<view class="checkbox"
									:class="{ checked: tempSelectedBondTypes.includes(option.value) }">
									<uni-icons v-if="tempSelectedBondTypes.includes(option.value)" type="checkmarkempty"
										size="14" color="#fff"></uni-icons>
								</view>
								<text class="option-label">{{ option.label }}</text>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="popup-footer">
					<view class="btn reset-btn" @click="resetBondTypes">重置</view>
					<view class="btn confirm-btn" @click="confirmBondTypes">确定</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import CustomHead from '@/components/head/head.vue';
import { getUnderwritingDetail } from '@/api/marketRate';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import { getAssetUrl } from '@/config/assets';




// ===================== 状态变量 =====================
/**
 * 承销明细数据存储
 * 从API获取的原始数据
 */
const underwritingDetail = ref([]);

/**
 * 日期相关状态
 * showDateRangePicker: 控制日期选择器的显示/隐藏
 * dateRange: 存储选择的开始日期和结束日期
 */
const showDateRangePicker = ref(false);
const dateRange = ref({ startDate: '', endDate: '' });

/**
 * 机构类型选择相关状态
 * selectedInstitutionTypes: 最终选中的机构类型列表
 * tempSelectedTypes: 临时选择状态，用于在弹窗中操作
 * institutionTypeOptions: 机构类型选项列表
 * institutionPopup: uni-popup引用
 */
const institutionPopup = ref(null);
const selectedInstitutionTypes = ref(['all']); // 默认选中"全部"
const tempSelectedTypes = ref(['all']); // 临时选择状态
const institutionTypeOptions = [
	{ label: '全部', value: 'all' },
	{ label: '银行', value: 'bank' },
	{ label: '券商', value: 'broker' }
];

/**
 * 债券类型选择相关状态
 * currentBondTypeTab: 当前选中的债券类型标签页（协会债/公司债）
 * selectedBondTypeOptions: 最终选中的债券类型列表
 * tempSelectedBondTypes: 临时选择状态，用于在弹窗中操作
 * bondTypePopup: uni-popup引用
 */
const bondTypePopup = ref(null);
const currentBondTypeTab = ref('association');
const selectedBondTypeOptions = ref([]);
const tempSelectedBondTypes = ref([]);

/**
 * 债券类型标签页定义
 */
const bondTypeTabs = [
	{ label: '协会债', value: 'association' },
	{ label: '公司债', value: 'company' }
];

/**
 * 债券类型选项配置
 * 按不同标签页分组
 */
const bondTypeOptions = {
	association: [
		{ label: '超短期融资券', value: 'ultra_short_financing' },
		{ label: '短期融资券', value: 'short_financing' },
		{ label: '中期票据', value: 'medium_term_note' },
		{ label: '集合票据', value: 'collective_note' },
		{ label: '定向工具', value: 'directional_tool' },
		{ label: '资产支持票据', value: 'asset_backed_note' },
		{ label: '项目收益票据', value: 'project_income_note' }
	],
	company: [
		{ label: '私募债', value: 'private_placement' },
		{ label: '一般公司债', value: 'general_corporate_bond' },
		{ label: '企业资产支持证券', value: 'enterprise_asset_backed_security' }
	]
};

// ===================== 计算属性 =====================
/**
 * 获取当前标签页下的债券类型选项列表
 * 根据currentBondTypeTab的值动态获取对应的选项列表
 */
const currentBondTypeOptions = computed(() => bondTypeOptions[currentBondTypeTab.value] || []);

/**
 * 处理后的公司列表数据
 * 1. 过滤掉总计项和其他项
 * 2. 计算各公司的承销金额和进度比例
 * 3. 按排名排序
 */
const displayCompanyList = computed(() => {
	if (!underwritingDetail.value?.length) return [];

	const totalItem = underwritingDetail.value.find(item => item.isTotal);
	const totalNetCollection = totalItem?.netCollection || 0;

	return underwritingDetail.value
		.filter(item => !item.isTotal && !item.isOther)
		.map(item => ({
			rank: item.ranking,
			name: item.sLuName,
			count: item.cnt,
			amount: (item.netCollection / 10000).toFixed(4), // 将金额从元转换为亿元
			progress: totalNetCollection > 0 ? (item.netCollection / totalNetCollection * 100).toFixed(2) : '0'
		}))
		.sort((a, b) => a.rank - b.rank);
});

/**
 * 总数据计算
 * 提取总计项的承销金额和笔数
 */
const totalData = computed(() => {
	const totalItem = underwritingDetail.value.find(item => item.isTotal);
	return {
		amount: totalItem ? (totalItem.netCollection / 10000).toFixed(4) : '0.0000', // 将金额从元转换为亿元
		count: totalItem?.cnt || 0
	};
});

// ===================== 数据请求 =====================

const searchValue = ref('');


/**
 * 获取承销明细数据
 * 根据筛选条件请求数据
 * - 当选择"全部"机构类型时，传递空数组
 * - 债券类型直接传递选中值
 */
const getUnderwritingDetailData = async () => {
	try {
		const res = await getUnderwritingDetail({
			issuerId: "",
			bondTypes: selectedBondTypeOptions.value,
			institutionType: selectedInstitutionTypes.value.includes('all') ? [] : selectedInstitutionTypes.value,
			issueStartDate: dateRange.value.startDate,
			issueEndDate: dateRange.value.endDate
		});
		underwritingDetail.value = res.data.data;
	} catch (error) {
		console.error('获取承销明细失败:', error);
	}
};

// ===================== 方法 =====================
// ----- 通用方法 -----
/**
 * 处理搜索操作
 * 当用户点击输入法搜索按钮时触发
 */
const handleSearch = () => {
	if (searchValue.value.trim()) {
		uni.navigateTo({
			url: `/subPageB/underwriting-details/detail-list?name=${searchValue.value.trim()}`
		});
	}
};

/**
 * 查看更多
 * 点击"查看更多"按钮时触发
 */
const viewMore = () => {
	console.log('查看更多');
};

// ----- 日期选择相关 -----
/**
 * 显示日期选择器
 */
const showDatePicker = () => {
	showDateRangePicker.value = true;
};

/**
 * 日期范围变更处理
 * @param {Array} dateArr - [startDate, endDate] 日期数组
 */
const onDateRangeChange = (dateArr) => {
	dateRange.value.startDate = dateArr[0];
	dateRange.value.endDate = dateArr[1];
	getUnderwritingDetailData();
};

// ----- 机构类型选择相关 -----
/**
 * 打开机构类型选择弹窗
 * 初始化临时选择状态
 */
const openInstitutionPopup = () => {
	tempSelectedTypes.value = [...selectedInstitutionTypes.value];
	institutionPopup.value.open();
};

/**
 * 关闭机构类型选择弹窗
 */
const closeInstitutionPopup = () => {
	institutionPopup.value.close();
};

/**
 * 切换机构类型选择状态
 * @param {string} value - 机构类型值
 * 
 * 特殊处理:
 * 1. 选择"全部"时清空其他选项
 * 2. 选择其他选项时取消"全部"选项
 * 3. 没有选中任何选项时自动选中"全部"
 */
const toggleInstitutionType = (value) => {
	const index = tempSelectedTypes.value.indexOf(value);

	if (value === 'all') {
		tempSelectedTypes.value = index === -1 ? ['all'] : [];
		return;
	}

	if (tempSelectedTypes.value.includes('all')) {
		tempSelectedTypes.value = [];
	}

	if (index === -1) {
		tempSelectedTypes.value.push(value);
	} else {
		tempSelectedTypes.value.splice(index, 1);
	}

	if (tempSelectedTypes.value.length === 0) {
		tempSelectedTypes.value = ['all'];
	}
};

/**
 * 重置机构类型选择
 * 恢复为默认选中"全部"
 */
const resetInstitutionTypes = () => {
	tempSelectedTypes.value = ['all'];
};

/**
 * 确认机构类型选择
 * 更新最终选择并刷新数据
 */
const confirmInstitutionTypes = () => {
	selectedInstitutionTypes.value = [...tempSelectedTypes.value];
	closeInstitutionPopup();
	getUnderwritingDetailData();
};

// ----- 债券类型选择相关 -----
/**
 * 打开债券类型选择弹窗
 * 初始化临时选择状态
 */
const openBondTypePopup = () => {
	tempSelectedBondTypes.value = [...selectedBondTypeOptions.value];
	bondTypePopup.value.open();
};

/**
 * 关闭债券类型选择弹窗
 */
const closeBondTypePopup = () => {
	bondTypePopup.value.close();
};

/**
 * 切换债券类型标签页
 * @param {string} tab - 标签页值
 */
const switchBondTypeTab = (tab) => {
	currentBondTypeTab.value = tab;
};

/**
 * 切换债券类型选项
 * @param {Object} option - 选项对象 {label, value}
 */
const toggleBondTypeOption = (option) => {
	const index = tempSelectedBondTypes.value.indexOf(option.value);
	if (index > -1) {
		tempSelectedBondTypes.value.splice(index, 1);
	} else {
		tempSelectedBondTypes.value.push(option.value);
	}
};

/**
 * 全选当前标签页的债券类型选项
 * 只影响当前标签页的选项，不影响其他标签页
 */
const selectAllBondTypeOptions = () => {
	const currentTabValues = currentBondTypeOptions.value.map(option => option.value);
	tempSelectedBondTypes.value = [
		...tempSelectedBondTypes.value.filter(value => !currentTabValues.includes(value)),
		...currentTabValues
	];
};

/**
 * 清空当前标签页的债券类型选项
 * 只影响当前标签页的选项，不影响其他标签页
 */
const clearBondTypeOptions = () => {
	const currentTabValues = currentBondTypeOptions.value.map(option => option.value);
	tempSelectedBondTypes.value = tempSelectedBondTypes.value.filter(value =>
		!currentTabValues.includes(value)
	);
};

/**
 * 反选当前标签页的债券类型选项
 * 已选变未选，未选变已选
 * 只影响当前标签页的选项，不影响其他标签页
 */
const invertBondTypeSelection = () => {
	const currentTabValues = currentBondTypeOptions.value.map(option => option.value);
	const selectedInCurrentTab = tempSelectedBondTypes.value.filter(value =>
		currentTabValues.includes(value)
	);
	const unselectedInCurrentTab = currentTabValues.filter(value =>
		!selectedInCurrentTab.includes(value)
	);

	tempSelectedBondTypes.value = [
		...tempSelectedBondTypes.value.filter(value => !selectedInCurrentTab.includes(value)),
		...unselectedInCurrentTab
	];
};

/**
 * 重置债券类型选择
 * 清空所有选择
 */
const resetBondTypes = () => {
	tempSelectedBondTypes.value = [];
};

/**
 * 确认债券类型选择
 * 更新最终选择并刷新数据
 */
const confirmBondTypes = () => {
	selectedBondTypeOptions.value = [...tempSelectedBondTypes.value];
	closeBondTypePopup();
	getUnderwritingDetailData();
};

// ===================== 生命周期钩子 =====================
/**
 * 组件挂载完成时的处理
 * 初始化数据
 */
onMounted(() => {
	getUnderwritingDetailData();
});

const viewDetail = (item) => {
	uni.navigateTo({
		url: `/subPageB/underwriting-details/detail-list?name=${item.name}`
	});
}
</script>

<style lang="scss" scoped>

/* 1.1 页面容器 */
.container {
	padding: 0 20rpx;
	height: 100vh;
	box-sizing: border-box;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	position: relative;
	overflow: hidden;
	/* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
	flex: 1;
	margin-top: 180rpx;
	padding: 20rpx 0;
	overflow: auto;
	/* 主滚动容器 */
	position: relative;
}

/* 1.4 滚动区域 */
.scrollable-content {
	height: 100%;

	::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
	}
}

/* 内容大卡片 */
.content-card {
	background-color: #FFFFFF;
	border-radius: 20rpx;
	padding: 20rpx;
	margin: 0 10rpx 30rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 搜索框 */
.search-box {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;

	.search-input {
		flex: 1;
		height: 72rpx;
		background: #ffffff;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 24rpx;

		uni-icons {
			margin-right: 10rpx;
		}

		input {
			flex: 1;
			height: 100%;
			font-size: 28rpx;
		}

		.placeholder {
			color: #999999;
		}
	}

	.cancel-btn {
		padding-left: 20rpx;
		font-size: 28rpx;
		color: #FF8E2B;
	}
}

/* 筛选条件 */
.filter-tabs {
	display: flex;
	justify-content: space-between;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 24rpx 0;

	.filter-tab {
		display: flex;
		align-items: center;

		text {
			font-size: 28rpx;
			color: #333333;
			margin-right: 8rpx;
		}

		.arrow {
			margin-left: 8rpx;
			font-size: 20rpx;
			color: #696969;
		}

		.selected-filter {
			font-size: 24rpx;
			color: #FF8E2B;
			max-width: 180rpx;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}

/* 总体数据 */
.total-data {
	background-color: #F6F5FF;
	border: 4rpx solid #ffffff;
	border-radius: 12rpx;
	padding:30rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-between;

	.total-item {
		.label {
			font-size: 28rpx;
			color: #666666;
			margin-right: 10rpx;
		}

		.value {
			font-size: 28rpx;
			font-weight: 500;
		}

		.highlight {
			color: #FF8E2B;
		}
	}
}

/* 公司列表 */
.company-list {
	.company-item {
		display: flex;
		align-items: center;
		border-radius: 12rpx;
		padding: 24rpx 0;
		padding-bottom: 40rpx;
		margin-bottom: 20rpx;
		position: relative;

		.rank {
			width: 40rpx;
			height: 40rpx;
			background: linear-gradient( 180deg, #C9C9C9 0%, #**********%);
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 20rpx;

			text {
				font-size: 26rpx;
				color: #FFFFFF;
				font-weight: 500;
			}

			&.rank-1 {
				background: linear-gradient(to right, #FFCB7D, #FF8E2B);
			}

			&.rank-2 {
				background: linear-gradient(to right, #B6E1E9, #88CDD7);
			}

			&.rank-3 {
				background: linear-gradient(to right, #FFC097, #FF8B5A);
			}
		}

		.company-info {
			flex: 1;

			.company-name {
				font-size: 26rpx;
				color: #333333;
				margin-bottom: 8rpx;
				font-weight: 500;
			}

			.deal-count {
				font-size: 26rpx;
				color: #999999;
				margin-left: 20rpx;
			}
		}

		.amount {
			font-size: 32rpx;
			color: #FF8E2B;
			font-weight: 500;
			margin-right: 20rpx;
			text-align: right;
			width: 160rpx;
		}

		.progress-container {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 20rpx;
			background-color: #EFEFEF;
			border-top-right-radius: 12rpx;
			border-bottom-right-radius: 12rpx;
			overflow: hidden;

			.progress-bar {
				height: 100%;
				background: linear-gradient(to right, #FFAF69, #FFD285 100%);
			}
		}
	}

	.view-more {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx 0;

		text {
			font-size: 26rpx;
			color: #999999;
			margin-top: 10rpx;
		}
	}
}

/* uni-popup内容样式 */
.popup-content {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	overflow: hidden;

	.popup-header {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 30rpx;
		position: relative;
		border-bottom: 1rpx solid #eee;

		.popup-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}

		.close-icon {
			position: absolute;
			right: 30rpx;
			font-size: 40rpx;
			color: #999;
		}
	}

	.popup-body {
		max-height: 600rpx;
		overflow: hidden;
		padding: 0 30rpx;

		.type-list {
			padding: 20rpx 0;
			max-height: 500rpx;
			overflow-y: auto;

			/* 隐藏滚动条 - 兼容各端 */
			scrollbar-width: none; /* Firefox */
			-ms-overflow-style: none; /* IE 10+ */

			&::-webkit-scrollbar {
				display: none; /* Chrome Safari */
				width: 0;
				height: 0;
				background: transparent;
			}

			.type-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 30rpx 0;
				border-bottom: 1rpx solid #eee;

				&.selected {
					color: #FF8E2B;
				}

				.type-name {
					font-size: 30rpx;
				}

				.checkbox {
					width: 40rpx;
					height: 40rpx;
					border-radius: 50%;
					background-color: #FF8E2B;
					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
					font-size: 24rpx;
				}
			}
		}
	}

	.popup-footer {
		display: flex;
		padding: 30rpx;
		border-top: 1rpx solid #eee;

		.btn {
			flex: 1;
			height: 80rpx;
			border-radius: 40rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 32rpx;
		}

		.reset-btn {
			margin-right: 20rpx;
			background-color: #f5f5f5;
			color: #333;
		}

		.confirm-btn {
			background-color: #FF8E2B;
			color: #fff;
		}
	}
}

/* 债券类型选择器弹窗内部样式 */
.popup-tabs {
	display: flex;
	padding: 30rpx 0;
	border-bottom: 1rpx solid #eee;
}

.popup-tab {
	padding: 15rpx 30rpx;
	font-size: 28rpx;
	color: #666;
	border-radius: 8rpx;
	margin-right: 20rpx;

	&.active {
		background-color: #FFF7ED;
		color: #FF8E2B;
	}
}

.popup-options {
	flex: 1;
	overflow-y: auto;
	padding: 20rpx 0;
	max-height: 50vh;
}

.options-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	font-size: 26rpx;
	color: #666;
}

.options-actions {
	display: flex;

	text {
		color: #FF8E2B;
		margin-left: 20rpx;
	}
}

.options-list {
	display: flex;
	flex-wrap: wrap;
}

.option-item {
	width: 50%;
	display: flex;
	align-items: center;
	padding: 10rpx 0;
}

.checkbox {
	width: 40rpx;
	height: 40rpx;
	border-radius: 8rpx;
	border: 1rpx solid #ddd;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 20rpx;

	&.checked {
		background-color: #FF8E2B;
		border-color: #FF8E2B;
	}
}

.option-label {
	font-size: 28rpx;
	color: #333;
}
</style>