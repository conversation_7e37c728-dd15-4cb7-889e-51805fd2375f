<template>
    <cover-view class="custom-popup" v-if="visible">
        <cover-view class="popup-mask" @click="cancelSelection"></cover-view>
        <cover-view class="popup-content" :class="{ 'popup-show': visible }">
            <cover-view class="popup-header">
                <cover-view class="popup-title">{{ title }}</cover-view>
            </cover-view>
            <cover-view class="flex-container">
                <cover-view v-for="(item, index) in options" :key="index" class="flex-item"
                    :class="{ 'item-selected': isItemSelected(item) }" @click="selectItem(item)">
                    {{ isObjectArray ? item[labelField] : item }}
                    <cover-view v-if="isItemSelected(item)" class="selected-triangle"></cover-view>
                </cover-view>
            </cover-view>
            <cover-view class="popup-footer">
                <cover-view class="footer-button cancel-button" @click="cancelSelection">取消</cover-view>
                <cover-view class="footer-button confirm-button" @click="confirmSelection">确定</cover-view>
            </cover-view>
        </cover-view>
    </cover-view>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

// 定义属性
const props = defineProps({
    // 弹窗可见性
    visible: {
        type: Boolean,
        default: false
    },
    // 弹窗标题
    title: {
        type: String,
        default: '请选择'
    },
    // 选项列表
    options: {
        type: Array,
        default: () => []
    },
    // 默认选中的值
    defaultSelected: {
        type: [String, Object],
        default: ''
    },
    // 对象数组时，用于显示的字段名
    labelField: {
        type: String,
        default: 'cnname'
    },
    // 对象数组时，用于值的字段名
    valueField: {
        type: String,
        default: 'itemcode'
    }
});

// 定义事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 当前临时选中项
const tempSelected = ref(props.defaultSelected);

// 判断options是否为对象数组
const isObjectArray = computed(() => {
    return props.options.length > 0 && typeof props.options[0] === 'object';
});

// 监听defaultSelected的变化，更新tempSelected
watch(() => props.defaultSelected, (newVal) => {
    if (newVal) {
        tempSelected.value = newVal;
    }
});

// 监听visible变化，重置临时选中项
watch(() => props.visible, (newVal) => {
    if (newVal) {
        tempSelected.value = props.defaultSelected;
    }
});

// 检查项目是否被选中
const isItemSelected = (item) => {
    if (isObjectArray.value) {
        // 对象数组情况
        if (typeof tempSelected.value === 'object') {
            return tempSelected.value[props.valueField] === item[props.valueField];
        } else {
            return tempSelected.value === item[props.valueField];
        }
    } else {
        // 字符串数组情况
        return tempSelected.value === item;
    }
};

// 选择项目
const selectItem = (item) => {
    if (isObjectArray.value) {
        tempSelected.value = item[props.valueField];
    } else {
        tempSelected.value = item;
    }
};

// 确认选择
const confirmSelection = () => {
    if (tempSelected.value) {
        // 如果是对象数组，需要找到对应的完整对象
        if (isObjectArray.value) {
            const selectedItem = props.options.find(item => item[props.valueField] === tempSelected.value);
            // 传递选中项的值
            emit('confirm', tempSelected.value, selectedItem);
        } else {
            emit('confirm', tempSelected.value);
        }
    }
    emit('update:visible', false);
};

// 取消选择
const cancelSelection = () => {
    emit('cancel');
    emit('update:visible', false);
};
</script>

<style lang="scss" scoped>
/* 自定义弹窗样式 */
.custom-popup {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
}

.popup-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.6);
}

.popup-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.popup-show {
    transform: translateY(0);
}

.popup-header {
    text-align: center;
    margin-bottom: 30rpx;
}

.popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

/* flex布局样式 */
.flex-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 20rpx 0;
    max-height: 400rpx;
    overflow-y: auto;

    /* 隐藏滚动条 - 兼容各端 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
        width: 0;
        height: 0;
        background: transparent;
    }
}

.flex-item {
    width: 30%;
    height: 80rpx;
    background-color: #f8f8f8;
    border-radius: 8rpx;
    text-align: center;
    line-height: 80rpx;
    position: relative;
    font-size: 30rpx;
    color: #333;
    margin-bottom: 20rpx;
    margin-right: 22rpx;
}

.item-selected {
    background-color: #FFF5E6;
    color: #FF9900;
}

.selected-triangle {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 0;
    height: 0;
    border-bottom: 30rpx solid #FF9900;
    border-left: 30rpx solid transparent;
}

/* 弹窗底部按钮样式 */
.popup-footer {
    display: flex;
    justify-content: space-between;
    margin-top: 40rpx;
}

.footer-button {
    width: 48%;
    height: 80rpx;
    border-radius: 8rpx;
    text-align: center;
    line-height: 80rpx;
    font-size: 32rpx;
}

.cancel-button {
    border: 2rpx solid #ccc;
    color: #666;
}

.confirm-button {
    background-color: #FF9900;
    color: white;
}
</style>