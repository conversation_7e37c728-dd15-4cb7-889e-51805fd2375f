<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="二级成交" :showBack="true" />
        </view>
        <view class="content-wrapper">
            <!-- 搜索框 -->
            <view class="search-box">
                <view class="search-input">
                    <uni-icons type="search" size="18" color="#999"></uni-icons>
                    <input type="text" placeholder="债券简称/债券代码" @input="handleInput" v-model="searchKeyword" placeholder-class="placeholder" />
                </view>
                <text class="cancel-btn" @tap="clearSearch">取消</text>
            </view>

            <!-- 卡片内容区 -->
            <view class="card-container">
                <!-- 卡片头部 -->
                <view class="card-header">
                    <view class="header-left">
                        <view class="title-icon"></view>
                        <text class="title-text">成交列表</text>
                    </view>
                    <view class="header-right">
                        <view class="filter-item" style="margin-right: 40rpx;" @tap="showFilterOptions('date')">
                            <text>交易日期</text>
                            <text class="arrow">▼</text>
                        </view>
                        <view class="filter-item" @tap="showFilterOptions('expired')">
                            <text>是否已到期</text>
                            <text class="arrow">▼</text>
                        </view>
                    </view>
                </view>

                <!-- 债券列表 - 使用SimpleTable组件 -->
                <view class="bonds-list">
                    <SimpleTable 
                        :columns="tableColumnsConfig" 
                        :stripe="false" 
                        :data="bondsList" 
                        :border="false"
                        :highlight="true" 
                        :loading="bondListLoading"
                        :hasMore="hasMore"
                        :height="tableHeight"
                        @cellClick="showBondDetail"
                        @loadMore="handleTableLoadMore"
                        :cell-style="cellStyle"
                    />
                    
                    <!-- 无数据提示 -->
                    <view v-if="!bondsList.length && !bondListLoading" class="no-data-tip">
                        <text class="no-data-text">暂无相关数据</text>
                    </view>

                    <!-- 没有更多数据提示 -->
                    <view v-if="!hasMore && bondsList.length > 0 && !bondListLoading" class="no-more-tip">
                        <text class="no-more-text">已显示全部数据</text>
                    </view>
                </view>
            </view>
        </view>
    </view>

    <!-- 日期范围选择 -->
    <DateRangePicker :visible="dateRangeVisible" @dateRangeChange="handleDateRangeChange"
        @update:visible="updateDateRangeVisible" />
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, onMounted, computed } from 'vue';
// 导入SimpleTable组件
import SimpleTable from '@/components/SimpleTable/index.vue';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import { getCustomListHead, getSql } from '@/api/dataDict';
import { getAssetUrl } from '@/config/assets';


// 搜索关键词
const searchKeyword = ref('');

// 日期范围选择相关
const dateRangeVisible = ref(false);
const startDate = ref('');
const endDate = ref('');

// 是否已到期相关
const expiredPopup = ref(null);
const currentExpiredStatus = ref(0); // 默认未到期，0-否，1-是
const expiredOptions = [
    { label: '否', value: 0 },
    { label: '是', value: 1 }
];

// 分页相关参数
const page = ref(1);
const pageSize = ref(20);
const bondListLoading = ref(false);
const hasMore = ref(true);

// 债券列表数据
const bondsList = ref<any[]>([]);

// 定义表格列的类型接口
interface TableColumn {
    columnCode: string;
    columnTitle: string;
    columnAlign?: string;
    defaultShow?: string;
    order?: number;
    columnWidth?: string;
}

interface TableColumnConfig {
    name: string;
    label: string;
    emptyString: string;
    align?: "left" | "center" | "right";
    fixed?: boolean;
    width?: number;
}

// 表格列定义
const tableColumns = ref<TableColumn[]>([]);

// 计算属性：表格列配置
const tableColumnsConfig = computed((): TableColumnConfig[] => {
    return tableColumns.value.map((col: TableColumn, index: number): TableColumnConfig => {
        const columnConfig: TableColumnConfig = {
            name: col.columnCode,  // 字段名
            label: col.columnTitle, // 显示名称
            emptyString: '--'
        };
        
        // 根据数据类型设置对齐方式
        if (col.columnAlign && ['left', 'center', 'right'].includes(col.columnAlign)) {
            columnConfig.align = col.columnAlign as "left" | "center" | "right";
        }
        
        // 第一列（债券简称）特殊处理
        if (index === 0) {
            columnConfig.fixed = true;  // 设置为固定列
            columnConfig.align = 'left';  // 债券简称左对齐
            columnConfig.width = 240;  // 设置合适的宽度
        }
        
        return columnConfig;
    });
});

// 获取自定义列表头展示项
const getCustomListHeadData = async () => {
    const params = {
        id: "800d2545336a4dc390a008b94cf1f032",
        moduleId: "1352320137612312576"
    }
    const res = await getCustomListHead(params);
    console.log('自定义列表头展示项', res);

    // 处理接口返回的数据
    if (res?.data?.data?.column && Array.isArray(res.data.data.column)) {
        const columnList: any[] = res.data.data.column;
        
        // 过滤出需要显示的列并排序
        const visibleColumns: TableColumn[] = columnList
            .filter((col: any) => col.defaultShow === 'Y' || !col.hasOwnProperty('defaultShow'))
            .sort((a: any, b: any) => (a.order || 0) - (b.order || 0))
            .map((col: any): TableColumn => ({
                columnCode: col.columnCode,
                columnTitle: col.columnTitle,
                columnAlign: col.columnAlign,
                defaultShow: col.defaultShow,
                order: col.order,
                columnWidth: col.columnWidth
            }));
        
        // 确保债券简称字段始终作为第一列
        const bondNameColumn = visibleColumns.find((col: TableColumn) => 
            col.columnCode === 'name' || 
            col.columnTitle === '债券简称' || 
            col.columnTitle.includes('债券简称') ||
            col.columnTitle.includes('简称')
        );
        
        // 重新排列列顺序
        let reorderedColumns: TableColumn[] = [];
        if (bondNameColumn) {
            const otherColumns = visibleColumns.filter((col: TableColumn) => col !== bondNameColumn);
            reorderedColumns = [bondNameColumn, ...otherColumns];
        } else {
            reorderedColumns = visibleColumns;
            // 如果没有找到债券简称列，添加一个默认的
            if (reorderedColumns.length === 0 || !reorderedColumns.some((col: TableColumn) => col.columnTitle.includes('简称'))) {
                reorderedColumns.unshift({
                    columnCode: 'name',
                    columnTitle: '债券简称',
                    columnAlign: 'left',
                    defaultShow: 'Y',
                    order: 0
                });
            }
        }
        
        tableColumns.value = reorderedColumns;
        console.log('处理后的表格列配置:', tableColumns.value);
    }
}

// 获取债券列表数据
const getBondListData = async (isLoadMore = false) => {
    // 如果是新查询，重置分页状态
    if (!isLoadMore) {
        page.value = 1;
        hasMore.value = true;
        bondListLoading.value = true;
    }

    const params = {
        params: {
            ownedModuleid: "1352320137612312576",
            ccid: "800d2545336a4dc390a008b94cf1f032",
            s_info_name: searchKeyword.value,
            include_expired: currentExpiredStatus.value,
            b_info_issuercode: '',
            startDate: startDate.value,
            endDate: endDate.value,
            // startDate: '',//初始不给日期是有数据的
            // endDate: ''
        },
        page: {
            pageNo: page.value,
            pageSize: pageSize.value,
            sort: null,
            direction: null
        }
    }
    
    try {
        const res = await getSql(params);
        console.log('债券列表数据', res);
        
        if (res?.data?.data?.pageInfo?.list) {
            const newList = res.data.data.pageInfo.list;
            
            // 如果是加载更多，合并数据，否则替换数据
            if (isLoadMore) {
                bondsList.value = [...bondsList.value, ...newList];
            } else {
                bondsList.value = newList;
            }
            
            // 检查是否还有更多数据
            if (newList.length < pageSize.value) {
                hasMore.value = false;
            }
        } else {
            if (!isLoadMore) {
                bondsList.value = [];
            }
            hasMore.value = false;
        }
    } catch (error) {
        console.error('获取债券列表数据失败', error);
        if (!isLoadMore) {
            bondsList.value = [];
        }
        hasMore.value = false;
    } finally {
        if (!isLoadMore) {
            bondListLoading.value = false;
        }
    }
}

// 输入框输入事件
const handleInput = (e) => {
    console.log('输入框输入事件', e);
}

// 清除搜索
const clearSearch = () => {
    searchKeyword.value = '';
    // 重置分页并获取数据
    getBondListData(false);
};

// 显示筛选选项
const showFilterOptions = (type: string) => {
    console.log('显示筛选选项:', type);
    // 根据筛选类型显示不同的选项
    if (type === 'date') {
        // 显示日期选择器
        showDateRangePicker();
    } else if (type === 'expired') {
        // 显示是否到期选项
        showExpiredPicker();
    }
};

// 显示债券详情
const showBondDetail = (row: any, column: any, rowIndex: number, colIndex: number) => {
    console.log('查看债券详情:', row, rowIndex);
    // 实现跳转到债券详情页的逻辑
    // uni.navigateTo({
    // 	url: `/subPageA/bond-detail/index?id=${row.code}`,
    // 	success: () => {
    // 		// 可以在这里进行一些跳转成功后的操作
    // 		console.log('跳转成功');
    // 		// 在全局状态或缓存中保存当前选中的债券数据，以便详情页使用
    // 		uni.setStorageSync('currentBondDetail', JSON.stringify(row));
    // 	},
    // 	fail: (err) => {
    // 		console.error('跳转失败', err);
    // 		// 如果页面不存在，提示用户
    // 		uni.showToast({
    // 			title: '详情页开发中，敬请期待',
    // 			icon: 'none',
    // 			duration: 2000
    // 		});
    // 	}
    // });
};

// 定义cellStyle函数
const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
    // 首列特殊处理：宽度自适应，显示完整文字
    if (columnIndex === 0) {
        return {
            width: 'auto',
            minWidth: '200rpx',
            whiteSpace: 'normal',
            overflow: 'visible',
            textOverflow: 'unset',
            wordBreak: 'break-word',
            lineHeight: '1.4'
        };
    }
    return {};
};

// 日期范围选择器相关函数
const showDateRangePicker = () => {
    dateRangeVisible.value = true;
};

const updateDateRangeVisible = (visible) => {
    dateRangeVisible.value = visible;
};

const handleDateRangeChange = (dateRange) => {
    console.log('选择的日期范围:', dateRange);
    startDate.value = dateRange[0];
    endDate.value = dateRange[1];

    // 筛选数据
    filterData();
};

// 是否已到期筛选相关函数
const showExpiredPicker = () => {
    // 使用uni的API打开弹窗
    uni.showActionSheet({
        itemList: ['否', '是'],
        success: function (res) {
            selectExpiredStatus(res.tapIndex);
            // 直接应用选择
            confirmExpiredSelection();
        },
        fail: function (res) {
            console.log(res.errMsg);
        }
    });
};

const selectExpiredStatus = (value) => {
    currentExpiredStatus.value = value;
};

const confirmExpiredSelection = () => {
    console.log('已选择的状态:', currentExpiredStatus.value);

    // 筛选数据
    filterData();
};

// 筛选数据
const filterData = () => {
    console.log('筛选数据:', {
        startDate: startDate.value,
        endDate: endDate.value,
        isExpired: currentExpiredStatus.value
    });
    
    // 重置分页并获取数据
    getBondListData(false);
};

// 表格加载更多回调
const handleTableLoadMore = () => {
    if (!bondListLoading.value && hasMore.value) {
        loadMoreBondData();
    }
};

// 加载更多数据
const loadMoreBondData = async () => {
    // 如果已经在加载中或没有更多数据，则不重复加载
    if (bondListLoading.value || !hasMore.value) return;

    // 设置加载中状态
    bondListLoading.value = true;

    try {
        // 页码加1
        page.value++;
        // 调用获取数据接口
        await getBondListData(true);
    } catch (error) {
        console.error('加载更多数据失败', error);
    } finally {
        // 加载完成
        bondListLoading.value = false;
    }
};

// 页面加载时获取数据
onMounted(() => {
    // 初始化默认日期区间为近三个月
    setDefaultDateRange();
    
    // 获取自定义列表头展示项
    getCustomListHeadData()
        .then(() => {
            // 获取列表头后，再获取债券列表数据
            getBondListData(false);
        })
        .catch(error => {
            console.error('获取自定义列表头失败', error);
            // 即使获取列表头失败，也要尝试获取债券列表数据
            getBondListData(false);
        });
});

// 设置默认日期区间为近三个月
const setDefaultDateRange = () => {
    const endDate_obj = new Date();
    const startDate_obj = new Date();
    startDate_obj.setMonth(startDate_obj.getMonth() - 3); // 当前日期的3个月前

    // 格式化日期为 'YYYY-MM-DD' 格式
    endDate.value = formatDate(endDate_obj);
    startDate.value = formatDate(startDate_obj);

    console.log('初始化日期区间:', startDate.value, '至', endDate.value);
};

// 日期格式化函数
const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
};

// 计算表格高度
const tableHeight = computed(() => {
    if (!bondsList.value.length && !bondListLoading.value) {
        // 无数据时，高度自适应
        return 'auto';
    }
    
    // 计算可用高度
    // content-wrapper 高度: calc(100vh - 180rpx)
    // search-box 高度: 约 112rpx (72rpx + 40rpx padding)
    // card-container padding: 40rpx (20rpx * 2)
    // card-header 高度: 约 85rpx
    // bonds-list padding: 20rpx (10rpx * 2)
    // 为底部提示预留空间: 80rpx
    // 总需要减去的高度: 180 + 112 + 40 + 85 + 20 + 80 = 517rpx
    
    return 'calc(100vh - 580rpx)';
});

</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
    flex: 1;
    margin-top: 180rpx;
    padding: 20rpx 0;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 180rpx);
    box-sizing: border-box;
}

/* 搜索框 */
.search-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    flex-shrink: 0;
}

.search-input {
    flex: 1;
    height: 72rpx;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);

    uni-icons {
        margin-right: 10rpx;
    }

    input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
    }

    .placeholder {
        color: #999999;
    }
}

.cancel-btn {
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #ff9500;
}

/* 卡片容器 */
.card-container {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 20rpx;
    margin-top: 10rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 卡片头部 */
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10rpx;
    margin-left: 10rpx;
    padding: 0 10rpx 25rpx;
    flex-shrink: 0;
}

.header-left {
    flex: 1;
    display: flex;
    align-items: center;
}

.title-icon {
    width: 48rpx;
    height: 52rpx;
    position: relative;
    top: -10rpx;
}

.title-icon::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(133, 111, 254, 0.6) 0%, rgba(255, 237, 221, 0) 100%);
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
}

.title-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    transform: translateX(-25rpx);
}

.header-right {
    display: flex;
    align-items: center;
}

.filter-item {
    display: flex;
    align-items: center;
    margin-left: 15rpx;
    font-size: 26rpx;
    color: #000;
    border-radius: 30rpx;
    height: 60rpx;
}

.arrow {
    margin-left: 5rpx;
    font-size: 20rpx;
    color: #000;
}

/* 债券列表 */
.bonds-list {
    flex: 1;
    overflow: auto;
    position: relative;
    padding: 10rpx 0;
}

/* 无数据提示样式 */
.no-data-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0 60rpx 0;
    .no-data-text {
        font-size: 28rpx;
        color: #999999;
    }
}

/* 没有更多数据提示 */
.no-more-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 0;
    
    .no-more-text {
        font-size: 24rpx;
        color: #c0c4cc;
        position: relative;
        
        &::before,
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 60rpx;
            height: 1rpx;
            background-color: #e4e7ed;
        }
        
        &::before {
            left: -80rpx;
        }
        
        &::after {
            right: -80rpx;
        }
    }
}

/* 弹窗样式 */
.popup-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 30rpx;
    max-height: 70vh;
    display: flex;
    flex-direction: column;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #F5F5F5;
}

.popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.close-icon {
    padding: 10rpx;
}

.popup-options {
    flex: 1;
    overflow: hidden;
    padding: 20rpx 0;
    max-height: 50vh;
}

.status-options-list {
    padding: 20rpx 0;
    max-height: 40vh;
    overflow-y: auto;

    /* 隐藏滚动条 - 兼容各端 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */

    &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
        width: 0;
        height: 0;
        background: transparent;
    }
}

.status-option-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
}

.radio {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    border: 2rpx solid #DCDFE6;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

.radio.checked {
    border-color: #FF9900;
}

.radio-inner {
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background-color: #FF9900;
}

.option-label {
    font-size: 28rpx;
    color: #333;
}

.popup-footer {
    padding-top: 30rpx;
    border-top: 2rpx solid #F5F5F5;
}

.confirm-btn {
    width: 100%;
    height: 88rpx;
    background: #FF9900;
    border-radius: 44rpx;
    color: #FFFFFF;
    font-size: 32rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
}

.confirm-btn:active {
    opacity: 0.9;
}
</style>