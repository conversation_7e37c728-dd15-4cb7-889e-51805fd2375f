<template>
    <view class="benchmark-mixed-chart-container" :style="{ height: `${chartHeight}px` }">
        <!-- 数据加载中提示 -->
        <view v-if="!chartData || !chartData.xAxisData || chartData.xAxisData.length === 0" class="loading-container">
            <text class="loading-text">图表数据加载中...</text>
        </view>

        <!-- 图表内容 -->
        <template v-else>
            <!-- 自定义图例 -->
            <view class="custom-legend" v-if="legendData.length > 0">
                <scroll-view class="legend-scroll" scroll-x="true" :show-scrollbar="false">
                    <view class="legend-items">
                        <view
                            v-for="(item, index) in legendData"
                            :key="index"
                            class="legend-item"
                            :class="{ 'legend-item-disabled': item.disabled }"
                            @click="toggleLegendItem(index)"
                        >
                            <view
                                class="legend-icon"
                                :style="{ backgroundColor: item.disabled ? '#ccc' : item.color }"
                            ></view>
                            <text class="legend-text" :style="{ color: item.disabled ? '#ccc' : '#666' }">
                                {{ item.name }}
                            </text>
                        </view>
                    </view>
                </scroll-view>
            </view>

            <!-- 图表容器 -->
            <view class="chart-wrapper">
                <l-echart
                    ref="chartRef"
                    @finished="onChartFinished"
                />
            </view>
        </template>
    </view>
</template>

<script setup>
import { ref, watch } from 'vue';

// 引入echarts（根据平台条件编译）
// #ifdef MP
const echarts = require('../../uni_modules/lime-echart/static/echarts.min.js');
// #endif
// #ifndef MP  
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min.js';
// #endif

// 引入 lime-echart 组件
import LEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue';

// Props定义
const props = defineProps({
    // 图表数据
    chartData: {
        type: Object,
        default: () => ({
            xAxisData: [],
            barSeries: [],
            lineSeries: []
        })
    },
    // 图表高度
    chartHeight: {
        type: Number,
        default: 350
    }
});

// 图表引用
const chartRef = ref(null);
// 图表实例
const chartInstance = ref(null);
// 图例数据
const legendData = ref([]);

// 初始化图例数据
const initLegendData = () => {
    if (!props.chartData) {
        legendData.value = [];
        return;
    }

    const legends = [];

    try {
        // 添加柱状图系列
        if (props.chartData.barSeries && Array.isArray(props.chartData.barSeries)) {
            props.chartData.barSeries.forEach(series => {
                if (series && series.name) {
                    legends.push({
                        name: series.name,
                        color: series.itemStyle?.color || '#5470c6',
                        type: 'bar',
                        disabled: false
                    });
                }
            });
        }

        // 添加折线图系列
        if (props.chartData.lineSeries && Array.isArray(props.chartData.lineSeries)) {
            props.chartData.lineSeries.forEach(series => {
                if (series && series.name) {
                    legends.push({
                        name: series.name,
                        color: series.itemStyle?.color || series.lineStyle?.color || '#fc8452',
                        type: 'line',
                        disabled: false
                    });
                }
            });
        }

        legendData.value = legends;
    } catch (error) {
        console.error('初始化图例数据失败:', error);
        legendData.value = [];
    }
};

// 切换图例项状态
const toggleLegendItem = (index) => {
    if (index >= 0 && index < legendData.value.length) {
        legendData.value[index].disabled = !legendData.value[index].disabled;
        updateChart();
    }
};

// 获取过滤后的系列数据
const getFilteredSeries = () => {
    if (!props.chartData) return [];

    const series = [];

    try {
        // 添加柱状图系列
        if (props.chartData.barSeries && Array.isArray(props.chartData.barSeries)) {
            props.chartData.barSeries.forEach(barSeries => {
                if (barSeries && barSeries.name) {
                    const legend = legendData.value.find(item => item.name === barSeries.name);
                    if (!legend || !legend.disabled) {
                        series.push(barSeries);
                    }
                }
            });
        }

        // 添加折线图系列
        if (props.chartData.lineSeries && Array.isArray(props.chartData.lineSeries)) {
            props.chartData.lineSeries.forEach(lineSeries => {
                if (lineSeries && lineSeries.name) {
                    const legend = legendData.value.find(item => item.name === lineSeries.name);
                    if (!legend || !legend.disabled) {
                        series.push(lineSeries);
                    }
                }
            });
        }
    } catch (error) {
        console.error('获取过滤系列数据失败:', error);
    }

    return series;
};

// 图表初始化完成回调
const onChartFinished = async () => {
    // 增加更长的延迟，确保DOM完全渲染和数据准备就绪
    setTimeout(async () => {
        if (!chartRef.value) {
            console.warn('图表引用不存在');
            return;
        }

        // 检查数据是否准备好
        if (!props.chartData || !props.chartData.xAxisData || props.chartData.xAxisData.length === 0) {
            console.warn('图表数据未准备好，延迟初始化');
            setTimeout(() => onChartFinished(), 500);
            return;
        }

        try {
            const myChart = await chartRef.value.init(echarts);
            if (myChart) {
                chartInstance.value = myChart;
                initLegendData();
                // 再次延迟设置配置，确保图表实例完全准备好
                setTimeout(() => {
                    if (chartInstance.value) {
                        chartInstance.value.setOption(getChartOption());
                    }
                }, 100);
            }
        } catch (error) {
            console.error('图表初始化失败:', error);
            // 如果初始化失败，尝试重新初始化
            setTimeout(() => onChartFinished(), 1000);
        }
    }, 500);
};

// 获取图表配置
const getChartOption = () => {
    // 如果没有数据，返回基本配置
    if (!props.chartData || !props.chartData.xAxisData) {
        return {
            grid: {
                left: 20,
                right: 20,
                bottom: 40,
                top: 60,
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: [],
                axisLine: {
                    show: true,
                    lineStyle: { color: '#E5E5E5' }
                }
            },
            yAxis: {
                type: 'value',
                axisLine: { show: false },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#F0F0F0',
                        type: 'dashed'
                    }
                }
            },
            series: []
        };
    }

    return {
        // 隐藏内置图例，使用自定义图例
        legend: {
            show: false
        },
        // 网格配置 - 调整顶部距离，为自定义图例留出空间
        grid: {
            left: 10,
            right: 10,
            bottom: 40,
            top: 40, // 减少顶部距离，因为图例已经外置
            containLabel: true
        },
        // X轴配置
        xAxis: {
            type: 'category',
            data: props.chartData.xAxisData,
            axisLabel: {
                rotate: -45,
                fontSize: 11,
                interval: 0,
                color: '#666',
                margin: 15,
                // 限制标签长度，避免过长
                formatter: function(value) {
                    if (value.length > 6) {
                        return value.substring(0, 6) + '...';
                    }
                    return value;
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#E5E5E5',
                    width: 1
                }
            },
            axisTick: {
                show: false
            }
        },
        // Y轴配置（双Y轴）
        yAxis: [
            {
                type: 'value',
                name: '金额(万元)',
                position: 'left',
                nameTextStyle: {
                    color: '#666',
                    fontSize: 11,
                    padding: [0, 0, 0, 0]
                },
                axisLabel: {
                    formatter: function(value) {
                        if (value >= 10000) {
                            return (value / 10000).toFixed(1) + 'w';
                        }
                        return value;
                    },
                    fontSize: 10,
                    color: '#666'
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#F0F0F0',
                        type: 'dashed',
                        width: 1
                    }
                }
            },
            {
                type: 'value',
                name: '增长率(%)',
                position: 'right',
                nameTextStyle: {
                    color: '#666',
                    fontSize: 11,
                    padding: [0, 0, 0, 0]
                },
                axisLabel: {
                    formatter: '{value}%',
                    fontSize: 10,
                    color: '#666'
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: { show: false }
            }
        ],
        // 系列数据 - 根据图例状态过滤
        series: getFilteredSeries(),
        // 提示框配置
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'transparent',
            textStyle: {
                color: '#fff',
                fontSize: 12
            },
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999',
                    width: 1,
                    type: 'dashed'
                },
                lineStyle: {
                    color: '#999',
                    width: 1,
                    type: 'dashed'
                }
            },
            formatter: function(params) {
                let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].name}</div>`;
                params.forEach(param => {
                    const unit = param.seriesType === 'line' ? '%' : '万元';
                    result += `<div style="margin: 2px 0;">${param.marker} ${param.seriesName}: <span style="font-weight: bold;">${param.value}${unit}</span></div>`;
                });
                return result;
            },
            confine: true
        },
        // 工具箱配置
        toolbox: {
            show: false
        }
    };
};

// 设置图表配置
const setChartOption = () => {
    try {
        if (chartInstance.value && props.chartData) {
            const option = getChartOption();
            if (option && option.series) {
                chartInstance.value.setOption(option, true);
            }
        }
    } catch (error) {
        console.error('设置图表配置失败:', error);
    }
};

// 更新图表配置
const updateChart = () => {
    try {
        if (chartInstance.value && props.chartData) {
            const option = getChartOption();
            if (option && option.series) {
                chartInstance.value.setOption(option, true);
            }
        } else if (chartRef.value && props.chartData) {
            // 如果图表实例不存在但引用存在，尝试重新初始化
            onChartFinished();
        }
    } catch (error) {
        console.error('更新图表配置失败:', error);
    }
};

// 监听数据变化，重新设置图表
watch(() => props.chartData, () => {
    initLegendData();
    updateChart();
}, { deep: true });

// 暴露方法供父组件调用
const initChart = () => {
    // 检查数据是否准备好
    if (!props.chartData || !props.chartData.xAxisData || props.chartData.xAxisData.length === 0) {
        console.warn('图表数据未准备好，无法初始化');
        return;
    }

    if (chartInstance.value) {
        setChartOption();
    } else if (chartRef.value) {
        onChartFinished();
    } else {
        // 如果组件引用也不存在，延迟重试
        setTimeout(() => {
            initChart();
        }, 200);
    }
};

// 重新渲染图表
const renderChart = () => {
    if (chartInstance.value) {
        chartInstance.value.resize();
        setChartOption();
    }
};

defineExpose({
    initChart,
    renderChart,
    updateChart
});
</script>

<style lang="scss" scoped>
.benchmark-mixed-chart-container {
    width: 100%;
    height: 100%;
    min-height: 300px;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;

    .loading-container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        min-height: 200px;

        .loading-text {
            font-size: 28rpx;
            color: #999;
        }
    }

    .custom-legend {
        padding: 15rpx 20rpx 25rpx 20rpx; // 增加底部间距，与图表分离
        border-bottom: 1rpx solid #f0f0f0;

        .legend-scroll {
            width: 100%;
            white-space: nowrap;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */

            // 隐藏滚动条
            &::-webkit-scrollbar {
                display: none;
            }
        }

        .legend-items {
            display: inline-flex;
            align-items: center;
            gap: 30rpx;
            min-width: 100%;

            .legend-item {
                display: flex;
                align-items: center;
                gap: 12rpx;
                cursor: pointer;
                transition: opacity 0.3s ease;
                white-space: nowrap;
                flex-shrink: 0;

                &.legend-item-disabled {
                    opacity: 0.5;
                }

                .legend-icon {
                    width: 28rpx;
                    height: 28rpx;
                    border-radius: 4rpx;
                    flex-shrink: 0;
                    transition: background-color 0.3s ease;
                }

                .legend-text {
                    font-size: 24rpx;
                    color: #666;
                    transition: color 0.3s ease;
                    line-height: 1.2;
                }

                &:hover {
                    .legend-icon {
                        transform: scale(1.1);
                    }
                }
            }
        }
    }

    .chart-wrapper {
        position: relative;
        width: 100%;
        flex: 1;
        min-height: 250px;
        overflow: hidden;
        box-sizing: border-box;
        padding: 0;
        margin: 0;
    }
}

// 确保 lime-echart 组件完全填充容器
:deep(.lime-echart) {
    width: 100% !important;
    height: 100% !important;

    .lime-echart__canvas {
        width: 100% !important;
        height: 100% !important;
    }
}
</style>
