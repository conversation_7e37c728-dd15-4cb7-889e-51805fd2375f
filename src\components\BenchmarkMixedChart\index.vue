<template>
    <view class="benchmark-mixed-chart-container" :style="{ height: `${chartHeight}px`, minHeight: '300px' }">
        <!-- 图表容器 -->
        <view class="chart-wrapper" :style="{ width: '100%', height: '100%', position: 'relative' }">
            <l-echart
                ref="chartRef"
                :style="{ width: '100%', height: '100%', minHeight: '300px' }"
                @finished="onChartFinished"
            />
        </view>
    </view>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue';

// 引入echarts（根据平台条件编译）
// #ifdef MP
const echarts = require('../../uni_modules/lime-echart/static/echarts.min.js');
// #endif
// #ifndef MP  
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min.js';
// #endif

// 引入 lime-echart 组件
import LEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue';

// Props定义
const props = defineProps({
    // 图表数据
    chartData: {
        type: Object,
        default: () => ({
            xAxisData: [],
            barSeries: [],
            lineSeries: []
        })
    },
    // 图表高度
    chartHeight: {
        type: Number,
        default: 350
    }
});

// 图表引用
const chartRef = ref(null);
// 图表实例
const chartInstance = ref(null);

// 图表初始化完成回调
const onChartFinished = async () => {
    // 根据文档建议，使用 setTimeout 确保组件节点已经被渲染到页面上
    setTimeout(async () => {
        if (!chartRef.value) return;
        try {
            const myChart = await chartRef.value.init(echarts);
            chartInstance.value = myChart;
            myChart.setOption(getChartOption());
        } catch (error) {
            console.error('图表初始化失败:', error);
        }
    }, 300);
};

// 获取图表配置
const getChartOption = () => {
    // 如果没有数据，返回基本配置
    if (!props.chartData || !props.chartData.xAxisData) {
        return {
            grid: {
                left: '10%',
                right: '10%',
                bottom: '20%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value'
            },
            series: []
        };
    }

    return {
        // 图例配置
        legend: {
            data: [
                ...props.chartData.barSeries.map(item => item.name),
                ...props.chartData.lineSeries.map(item => item.name)
            ],
            top: 10,
            textStyle: {
                fontSize: 12
            }
        },
        // 网格配置
        grid: {
            left: '10%',
            right: '10%',
            bottom: '20%',
            top: '15%',
            containLabel: true
        },
        // X轴配置
        xAxis: {
            type: 'category',
            data: props.chartData.xAxisData,
            axisLabel: {
                rotate: -45,
                fontSize: 10,
                interval: 0
            },
            axisLine: {
                show: true,
                lineStyle: { color: '#E5E5E5' }
            },
            axisTick: {
                show: false
            }
        },
        // Y轴配置（双Y轴）
        yAxis: [
            {
                type: 'value',
                name: '金额(万元)',
                position: 'left',
                axisLabel: {
                    formatter: '{value}',
                    fontSize: 10
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#F0F0F0',
                        type: 'dashed'
                    }
                }
            },
            {
                type: 'value',
                name: '增长率(%)',
                position: 'right',
                axisLabel: {
                    formatter: '{value}%',
                    fontSize: 10
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: { show: false }
            }
        ],
        // 系列数据
        series: [
            ...props.chartData.barSeries,
            ...props.chartData.lineSeries
        ],
        // 提示框配置
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            },
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    const unit = param.seriesType === 'line' ? '%' : '万元';
                    result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`;
                });
                return result;
            }
        },
        // 工具箱配置
        toolbox: {
            show: false
        }
    };
};

// 设置图表配置
const setChartOption = () => {
    if (chartInstance.value) {
        chartInstance.value.setOption(getChartOption(), true);
    }
};

// 更新图表配置
const updateChart = () => {
    if (chartRef.value) {
        chartRef.value.setOption(getChartOption());
    }
};

// 监听数据变化，重新设置图表
watch(() => props.chartData, () => {
    updateChart();
}, { deep: true });

// 暴露方法供父组件调用
const initChart = () => {
    if (chartInstance.value) {
        setChartOption();
    } else {
        onChartFinished();
    }
};

// 重新渲染图表
const renderChart = () => {
    if (chartInstance.value) {
        chartInstance.value.resize();
        setChartOption();
    }
};

defineExpose({
    initChart,
    renderChart,
    updateChart
});
</script>

<style lang="scss" scoped>
.benchmark-mixed-chart-container {
    width: 100%;
    min-height: 300px;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    box-sizing: border-box;

    .chart-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        min-height: 300px;
        overflow: hidden;
        box-sizing: border-box;
    }
}
</style>
