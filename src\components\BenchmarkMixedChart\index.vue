<template>
    <view class="benchmark-mixed-chart-container" :style="{ height: `${chartHeight}px` }">
        <!-- 自定义图例 -->
        <view class="custom-legend">
            <scroll-view class="legend-scroll" scroll-x="true" :show-scrollbar="false">
                <view class="legend-items">
                    <view
                        v-for="(item, index) in legendData"
                        :key="index"
                        class="legend-item"
                        :class="{ 'legend-item-disabled': item.disabled }"
                        @click="toggleLegendItem(index)"
                    >
                        <view
                            class="legend-icon"
                            :style="{ backgroundColor: item.disabled ? '#ccc' : item.color }"
                        ></view>
                        <text class="legend-text" :style="{ color: item.disabled ? '#ccc' : '#666' }">
                            {{ item.name }}
                        </text>
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 图表容器 -->
        <view class="chart-wrapper">
            <l-echart
                ref="chartRef"
                @finished="onChartFinished"
            />
        </view>
    </view>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue';

// 引入echarts（根据平台条件编译）
// #ifdef MP
const echarts = require('../../uni_modules/lime-echart/static/echarts.min.js');
// #endif
// #ifndef MP  
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min.js';
// #endif

// 引入 lime-echart 组件
import LEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart.vue';

// Props定义
const props = defineProps({
    // 图表数据
    chartData: {
        type: Object,
        default: () => ({
            xAxisData: [],
            barSeries: [],
            lineSeries: []
        })
    },
    // 图表高度
    chartHeight: {
        type: Number,
        default: 350
    }
});

// 图表引用
const chartRef = ref(null);
// 图表实例
const chartInstance = ref(null);
// 图例数据
const legendData = ref([]);

// 初始化图例数据
const initLegendData = () => {
    if (!props.chartData) return;

    const legends = [];

    // 添加柱状图系列
    if (props.chartData.barSeries) {
        props.chartData.barSeries.forEach(series => {
            legends.push({
                name: series.name,
                color: series.itemStyle?.color || '#5470c6',
                type: 'bar',
                disabled: false
            });
        });
    }

    // 添加折线图系列
    if (props.chartData.lineSeries) {
        props.chartData.lineSeries.forEach(series => {
            legends.push({
                name: series.name,
                color: series.itemStyle?.color || series.lineStyle?.color || '#fc8452',
                type: 'line',
                disabled: false
            });
        });
    }

    legendData.value = legends;
};

// 切换图例项状态
const toggleLegendItem = (index) => {
    if (index >= 0 && index < legendData.value.length) {
        legendData.value[index].disabled = !legendData.value[index].disabled;
        updateChart();
    }
};

// 获取过滤后的系列数据
const getFilteredSeries = () => {
    if (!props.chartData) return [];

    const series = [];

    // 添加柱状图系列
    if (props.chartData.barSeries) {
        props.chartData.barSeries.forEach(barSeries => {
            const legend = legendData.value.find(item => item.name === barSeries.name);
            if (!legend || !legend.disabled) {
                series.push(barSeries);
            }
        });
    }

    // 添加折线图系列
    if (props.chartData.lineSeries) {
        props.chartData.lineSeries.forEach(lineSeries => {
            const legend = legendData.value.find(item => item.name === lineSeries.name);
            if (!legend || !legend.disabled) {
                series.push(lineSeries);
            }
        });
    }

    return series;
};

// 图表初始化完成回调
const onChartFinished = async () => {
    // 根据文档建议，使用 setTimeout 确保组件节点已经被渲染到页面上
    setTimeout(async () => {
        if (!chartRef.value) return;
        try {
            const myChart = await chartRef.value.init(echarts);
            chartInstance.value = myChart;
            initLegendData();
            myChart.setOption(getChartOption());
        } catch (error) {
            console.error('图表初始化失败:', error);
        }
    }, 300);
};

// 获取图表配置
const getChartOption = () => {
    // 如果没有数据，返回基本配置
    if (!props.chartData || !props.chartData.xAxisData) {
        return {
            grid: {
                left: 20,
                right: 20,
                bottom: 40,
                top: 60,
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: [],
                axisLine: {
                    show: true,
                    lineStyle: { color: '#E5E5E5' }
                }
            },
            yAxis: {
                type: 'value',
                axisLine: { show: false },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#F0F0F0',
                        type: 'dashed'
                    }
                }
            },
            series: []
        };
    }

    return {
        // 隐藏内置图例，使用自定义图例
        legend: {
            show: false
        },
        // 网格配置 - 调整顶部距离，为自定义图例留出空间
        grid: {
            left: 20,
            right: 20,
            bottom: 40,
            top: 20, // 减少顶部距离，因为图例已经外置
            containLabel: true
        },
        // X轴配置
        xAxis: {
            type: 'category',
            data: props.chartData.xAxisData,
            axisLabel: {
                rotate: -45,
                fontSize: 11,
                interval: 0,
                color: '#666',
                margin: 15,
                // 限制标签长度，避免过长
                formatter: function(value) {
                    if (value.length > 6) {
                        return value.substring(0, 6) + '...';
                    }
                    return value;
                }
            },
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#E5E5E5',
                    width: 1
                }
            },
            axisTick: {
                show: false
            }
        },
        // Y轴配置（双Y轴）
        yAxis: [
            {
                type: 'value',
                name: '金额(万元)',
                position: 'left',
                nameTextStyle: {
                    color: '#666',
                    fontSize: 11,
                    padding: [0, 0, 0, 0]
                },
                axisLabel: {
                    formatter: function(value) {
                        if (value >= 10000) {
                            return (value / 10000).toFixed(1) + 'w';
                        }
                        return value;
                    },
                    fontSize: 10,
                    color: '#666'
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#F0F0F0',
                        type: 'dashed',
                        width: 1
                    }
                }
            },
            {
                type: 'value',
                name: '增长率(%)',
                position: 'right',
                nameTextStyle: {
                    color: '#666',
                    fontSize: 11,
                    padding: [0, 0, 0, 0]
                },
                axisLabel: {
                    formatter: '{value}%',
                    fontSize: 10,
                    color: '#666'
                },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: { show: false }
            }
        ],
        // 系列数据 - 根据图例状态过滤
        series: getFilteredSeries(),
        // 提示框配置
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'transparent',
            textStyle: {
                color: '#fff',
                fontSize: 12
            },
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999',
                    width: 1,
                    type: 'dashed'
                },
                lineStyle: {
                    color: '#999',
                    width: 1,
                    type: 'dashed'
                }
            },
            formatter: function(params) {
                let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].name}</div>`;
                params.forEach(param => {
                    const unit = param.seriesType === 'line' ? '%' : '万元';
                    result += `<div style="margin: 2px 0;">${param.marker} ${param.seriesName}: <span style="font-weight: bold;">${param.value}${unit}</span></div>`;
                });
                return result;
            },
            confine: true
        },
        // 工具箱配置
        toolbox: {
            show: false
        }
    };
};

// 设置图表配置
const setChartOption = () => {
    if (chartInstance.value) {
        chartInstance.value.setOption(getChartOption(), true);
    }
};

// 更新图表配置
const updateChart = () => {
    if (chartRef.value) {
        chartRef.value.setOption(getChartOption());
    }
};

// 监听数据变化，重新设置图表
watch(() => props.chartData, () => {
    initLegendData();
    updateChart();
}, { deep: true });

// 暴露方法供父组件调用
const initChart = () => {
    if (chartInstance.value) {
        setChartOption();
    } else {
        onChartFinished();
    }
};

// 重新渲染图表
const renderChart = () => {
    if (chartInstance.value) {
        chartInstance.value.resize();
        setChartOption();
    }
};

defineExpose({
    initChart,
    renderChart,
    updateChart
});
</script>

<style lang="scss" scoped>
.benchmark-mixed-chart-container {
    width: 100%;
    height: 100%;
    min-height: 300px;
    background-color: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;

    .custom-legend {
        padding: 15rpx 20rpx 25rpx 20rpx; // 增加底部间距，与图表分离
        border-bottom: 1rpx solid #f0f0f0;

        .legend-scroll {
            width: 100%;
            white-space: nowrap;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */

            // 隐藏滚动条
            &::-webkit-scrollbar {
                display: none;
            }
        }

        .legend-items {
            display: inline-flex;
            align-items: center;
            gap: 30rpx;
            min-width: 100%;

            .legend-item {
                display: flex;
                align-items: center;
                gap: 12rpx;
                cursor: pointer;
                transition: opacity 0.3s ease;
                white-space: nowrap;
                flex-shrink: 0;

                &.legend-item-disabled {
                    opacity: 0.5;
                }

                .legend-icon {
                    width: 28rpx;
                    height: 28rpx;
                    border-radius: 4rpx;
                    flex-shrink: 0;
                    transition: background-color 0.3s ease;
                }

                .legend-text {
                    font-size: 24rpx;
                    color: #666;
                    transition: color 0.3s ease;
                    line-height: 1.2;
                }

                &:hover {
                    .legend-icon {
                        transform: scale(1.1);
                    }
                }
            }
        }
    }

    .chart-wrapper {
        position: relative;
        width: 100%;
        flex: 1;
        min-height: 250px;
        overflow: hidden;
        box-sizing: border-box;
        padding: 0;
        margin: 0;
    }
}

// 确保 lime-echart 组件完全填充容器
:deep(.lime-echart) {
    width: 100% !important;
    height: 100% !important;

    .lime-echart__canvas {
        width: 100% !important;
        height: 100% !important;
    }
}
</style>
