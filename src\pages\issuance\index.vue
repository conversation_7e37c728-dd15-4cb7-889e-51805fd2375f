<template>
    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
        <!-- 固定的头部区域 -->
        <view class="fixed-header">
            <CustomHead title="发行查询" :showBack="false" />
        </view>
        <view class="content-wrapper">
            <scroll-view class="scrollable-content" scroll-y :show-scrollbar="false" enhanced>
                <!-- 搜索框 -->
                <view class="search-box">
                    <view class="search-input">
                        <uni-icons type="search" size="18" color="#999"></uni-icons>
                        <input type="text" v-model="queryParams.keyWords" placeholder="债券简称/发行人"
                            placeholder-class="placeholder" @confirm="handleSearch" />
                    </view>
                    <text class="cancel-btn" @tap="clearSearch">取消</text>
                </view>

                <!-- 卡片内容区 -->
                <view class="card-container">
                    <!-- 筛选条件 -->
                    <view>
                        <view class="filter-row">
                            <view class="filter-item" @tap="showFilterOptions('status')">
                                <text>发行状态</text>
                                <text class="arrow">▼</text>
                            </view>
                            <view class="filter-item" @tap="showFilterOptions('type')">
                                <text>债券类型</text>
                                <text class="arrow">▼</text>
                            </view>
                            <view class="filter-item" @tap="showFilterOptions('rating')">
                                <text>主体评级</text>
                                <text class="arrow">▼</text>
                            </view>
                            <view class="filter-item" @tap="showFilterOptions('debtRating')">
                                <text>债项评级</text>
                                <text class="arrow">▼</text>
                            </view>
                        </view>
                        <scroll-view class="filter-row" scroll-x :show-scrollbar="false" enhanced>
                            <view class="filter-item secondary" @tap="showFilterOptions('property')">
                                <text>主体性质</text>
                                <text class="arrow">▼</text>
                            </view>
                            <view class="filter-item secondary" @tap="showFilterOptions('area')">
                                <text>区域选择</text>
                                <text class="arrow">▼</text>
                            </view>
                            <view class="filter-item secondary" @tap="showFilterOptions('industry')">
                                <text>行业选择</text>
                                <text class="arrow">▼</text>
                            </view>
                            <view v-if="shouldShowDateButton" class="filter-item secondary"
                                @tap="showFilterOptions('date')">
                                <text>发行起始日期</text>
                                <text class="arrow">▼</text>
                            </view>
                        </scroll-view>
                    </view>

                    <!-- 债券列表 - 使用SimpleTable组件 -->
                    <view class="bonds-list">
                        <SimpleTable 
                            :columns="tableColumns" 
                            :stripe="false" 
                            :data="bondsList" 
                            :border="false"
                            :highlight="true" 
                            :loading="isLoading"
                            :hasMore="hasMore"
                            :height="bondsList.length > 0 ? '800rpx' : 'auto'"
                            @cellClick="showBondDetailFromCell"
                            @loadMore="loadMore"
                            :cell-style="cellStyle"
                        />
                        
                        <!-- 无数据提示 -->
                        <view v-if="!bondsList.length && !isLoading" class="no-data-tip">
                            <text class="no-data-text">暂无相关数据</text>
                        </view>

                        <!-- 没有更多数据提示 -->
                        <view v-if="!hasMore && bondsList.length > 0 && !isLoading" class="no-more-tip">
                            <text class="no-more-text">已显示全部数据</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>


        <!-- 发行状态选择弹窗 -->
        <uni-popup ref="statusPopup" type="bottom" :safe-area="false" @change="onStatusPopupChange">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">发行状态选择</text>
                    <view class="close-icon" @tap="closeStatusPicker">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="status-options-list">
                        <view v-for="(option, index) in statusOptions" :key="index" class="status-option-item"
                            @tap="selectStatus(option.value)">
                            <view class="radio" :class="{ checked: currentStatus === option.value }">
                                <view class="radio-inner" v-if="currentStatus === option.value"></view>
                            </view>
                            <text class="option-label">{{ option.label }}</text>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @tap="confirmStatusSelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 债券类型选择弹窗 -->
        <uni-popup ref="bondTypePopup" type="bottom" :safe-area="false" @change="onBondTypePopupChange">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">债券类型选择</text>
                    <view class="close-icon" @tap="closeBondTypePicker">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>默认勾选全部</text>
                        <view class="options-actions">
                            <text @tap="selectAllBondTypes">全选</text>
                            <text @tap="clearBondTypes">清空</text>
                            <text @tap="invertBondTypes">反选</text>
                        </view>
                    </view>

                    <view class="options-list">
                        <view v-for="(category, categoryIndex) in bondTypeOptions" :key="categoryIndex">
                            <view class="category-title">{{ category.bondTypeName }}</view>
                            <view class="category-options">
                                <view v-for="(option, index) in category.list" :key="index" class="option-item"
                                    @tap="toggleBondType(option.bondTypeCode)">
                                    <view class="checkbox"
                                        :class="{ checked: selectedBondTypes.includes(option.bondTypeCode) }">
                                        <uni-icons v-if="selectedBondTypes.includes(option.bondTypeCode)"
                                            type="checkmarkempty" size="14" color="#fff"></uni-icons>
                                    </view>
                                    <text class="option-label">{{ option.bondTypeName }}</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @tap="confirmBondTypeSelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 主体评级选择弹窗 -->
        <uni-popup ref="subjectRatingPopup" type="bottom" :safe-area="false" @change="onSubjectRatingPopupChange">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">主体评级选择</text>
                    <view class="close-icon" @tap="closeSubjectRatingPicker">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>默认勾选全部</text>
                        <view class="options-actions">
                            <text @tap="selectAllSubjectRatings">全选</text>
                            <text @tap="clearSubjectRatings">清空</text>
                            <text @tap="invertSubjectRatings">反选</text>
                        </view>
                    </view>

                    <view class="options-list">
                        <view class="category-options">
                            <view v-for="(option, index) in subjectRatingOptions" :key="index" class="option-item"
                                @tap="toggleSubjectRating(option)">
                                <view class="checkbox" :class="{ checked: selectedSubjectRatings.includes(option) }">
                                    <uni-icons v-if="selectedSubjectRatings.includes(option)" type="checkmarkempty"
                                        size="14" color="#fff"></uni-icons>
                                </view>
                                <text class="option-label">{{ option }}</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @tap="confirmSubjectRatingSelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 债项评级选择弹窗 -->
        <uni-popup ref="debtRatingPopup" type="bottom" :safe-area="false" @change="onDebtRatingPopupChange">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">债项评级选择</text>
                    <view class="close-icon" @tap="closeDebtRatingPicker">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>默认勾选全部</text>
                        <view class="options-actions">
                            <text @tap="selectAllDebtRatings">全选</text>
                            <text @tap="clearDebtRatings">清空</text>
                            <text @tap="invertDebtRatings">反选</text>
                        </view>
                    </view>

                    <view class="options-list">
                        <view class="category-options">
                            <view v-for="(option, index) in debtRatingOptions" :key="index" class="option-item"
                                @tap="toggleDebtRating(option)">
                                <view class="checkbox" :class="{ checked: selectedDebtRatings.includes(option) }">
                                    <uni-icons v-if="selectedDebtRatings.includes(option)" type="checkmarkempty"
                                        size="14" color="#fff"></uni-icons>
                                </view>
                                <text class="option-label">{{ option }}</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @tap="confirmDebtRatingSelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 主体性质选择弹窗 -->
        <uni-popup ref="subjectPropertyPopup" type="bottom" :safe-area="false" @change="onSubjectPropertyPopupChange">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">主体性质选择</text>
                    <view class="close-icon" @tap="closeSubjectPropertyPicker">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-list">
                        <view class="category-options">
                            <view v-for="(option, index) in filteredSubjectPropertyList" :key="index"
                                class="option-item" @tap="toggleSubjectProperty(option.itemcode)">
                                <view class="checkbox"
                                    :class="{ checked: selectedSubjectProperties.includes(option.itemcode) }">
                                    <uni-icons v-if="selectedSubjectProperties.includes(option.itemcode)"
                                        type="checkmarkempty" size="14" color="#fff"></uni-icons>
                                </view>
                                <text class="option-label">{{ option.cnname }}</text>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @tap="confirmSubjectPropertySelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 区域选择组件 -->
        <AreaSelector v-model:visible="areaVisible" :defaultSelected="selectedAreaIds" @areaChange="handleAreaChange"
            @close="onAreaClose" :key="areaSelectKey" />

        <!-- 行业选择弹窗 -->
        <uni-popup ref="industryPopup" type="bottom" :safe-area="false" @change="onIndustryPopupChange">
            <view class="popup-content">
                <view class="popup-header">
                    <text class="popup-title">行业选择</text>
                    <view class="close-icon" @tap="closeIndustryPicker">
                        <uni-icons type="close" size="20" color="#333"></uni-icons>
                    </view>
                </view>

                <view class="popup-options">
                    <view class="options-header">
                        <text>树形结构选择</text>
                        <view class="options-actions">
                            <text @tap="selectAllIndustries">全选</text>
                            <text @tap="clearIndustries">清空</text>
                            <text @tap="invertIndustries">反选</text>
                            <text @tap="expandAll">展开全部</text>
                            <text @tap="collapseAll">收起全部</text>
                        </view>
                    </view>

                    <scroll-view class="tree-container" scroll-y>
                        <!-- 一级分类 -->
                        <view v-for="(level1, index1) in industryData" :key="index1" class="tree-level">
                            <view class="tree-item level-1" @tap="toggleLevel1(level1.sInfoCompindCode)">
                                <view class="tree-content">
                                    <view class="checkbox"
                                        :class="{ checked: isLevel1Selected(level1), indeterminate: isLevel1Indeterminate(level1) }"
                                        @tap.stop="toggleLevel1Selection(level1)">
                                        <uni-icons v-if="isLevel1Selected(level1)" type="checkmarkempty" size="14"
                                            color="#fff"></uni-icons>
                                        <view v-else-if="isLevel1Indeterminate(level1)" class="indeterminate-icon">
                                        </view>
                                    </view>
                                    <text class="tree-label">{{ level1.sInfoCompindName }}</text>
                                </view>
                            </view>

                            <!-- 二级分类 -->
                            <view v-if="expandedLevel1.includes(level1.sInfoCompindCode)" class="tree-children">
                                <view v-for="(level2, index2) in level1.list" :key="index2" class="tree-level">
                                    <view class="tree-item level-2" @tap="toggleLevel2(level2.sInfoCompindCode)">
                                        <view class="tree-content">
                                            <view class="checkbox"
                                                :class="{ checked: isLevel2Selected(level2), indeterminate: isLevel2Indeterminate(level2) }"
                                                @tap.stop="toggleLevel2Selection(level2)">
                                                <uni-icons v-if="isLevel2Selected(level2)" type="checkmarkempty"
                                                    size="14" color="#fff"></uni-icons>
                                                <view v-else-if="isLevel2Indeterminate(level2)"
                                                    class="indeterminate-icon"></view>
                                            </view>
                                            <text class="tree-label">{{ level2.sInfoCompindName }}</text>
                                        </view>
                                    </view>

                                    <!-- 三级分类 -->
                                    <view v-if="expandedLevel2.includes(level2.sInfoCompindCode)" class="tree-children">
                                        <view v-for="(level3, index3) in level2.list" :key="index3" class="tree-level">
                                            <view class="tree-item level-3"
                                                @tap="toggleLevel3(level3.sInfoCompindCode)">
                                                <view class="tree-content">
                                                    <view class="checkbox"
                                                        :class="{ checked: isLevel3Selected(level3), indeterminate: isLevel3Indeterminate(level3) }"
                                                        @tap.stop="toggleLevel3Selection(level3)">
                                                        <uni-icons v-if="isLevel3Selected(level3)" type="checkmarkempty"
                                                            size="14" color="#fff"></uni-icons>
                                                        <view v-else-if="isLevel3Indeterminate(level3)"
                                                            class="indeterminate-icon"></view>
                                                    </view>
                                                    <text class="tree-label">{{ level3.sInfoCompindName }}</text>
                                                </view>
                                            </view>

                                            <!-- 四级分类（可选择） -->
                                            <view v-if="expandedLevel3.includes(level3.sInfoCompindCode)"
                                                class="tree-children">
                                                <view v-for="(level4, index4) in level3.list" :key="index4"
                                                    class="tree-level">
                                                    <view class="tree-item level-4 selectable"
                                                        @tap="toggleIndustry(level4.sInfoCompindCode)">
                                                        <view class="tree-content">
                                                            <view class="checkbox"
                                                                :class="{ checked: selectedIndustries.includes(level4.sInfoCompindCode) }">
                                                                <uni-icons
                                                                    v-if="selectedIndustries.includes(level4.sInfoCompindCode)"
                                                                    type="checkmarkempty" size="14"
                                                                    color="#fff"></uni-icons>
                                                            </view>
                                                            <text class="tree-label">{{ level4.sInfoCompindName
                                                                }}</text>
                                                        </view>
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </scroll-view>
                </view>

                <view class="popup-footer">
                    <button class="confirm-btn" @tap="confirmIndustrySelection">确认</button>
                </view>
            </view>
        </uni-popup>

        <!-- 发行起始日期范围选择 -->
        <DateRangePicker :visible="dateRangeVisible" :defaultStartDate="defaultStartDate"
            :defaultEndDate="defaultEndDate" @dateRangeChange="handleDateRangeChange"
            @update:visible="updateDateRangeVisible" />

        <!-- 自定义tabBar -->
        <AppTabBar v-if="showTabBar" :selectNumber="1" :permissionData="getTabBarPermissions" />
    </view>
</template>

<script setup lang="ts">
import CustomHead from '@/components/head/head.vue';
import { ref, onMounted, computed, watch } from 'vue';
// 导入SimpleTable组件替换zb-table组件
import SimpleTable from '@/components/SimpleTable/index.vue';
import AppTabBar from '@/components/AppTabBar/index.vue';
import { onShow } from '@dcloudio/uni-app';
import { getBondType, getSubjectProperty, getObtainData, getIssuanceList, getCustomListHead, getSql } from '@/api/dataDict';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import AreaSelector from '@/components/AreaSelector/index.vue';
import { usePermissionStore } from '@/stores/permission';
import { storeToRefs } from 'pinia';
import { getAssetUrl } from '@/config/assets';


// 查询条件类型定义
interface QueryParams {
    listDateType: string;
    firstDtStart: string;
    firstDtEnd: string;
    compPropertyList: string[];
    creditRatingList: string[];
    districtCodeList: string[];
    compindCodeList: string[];
    bondRatingList: string[];
    bondTypeList: string[];
    termList: string[];
    keyWords: string;
    cnbdCreditRatingList: string[];
    productCodeList: string[];
    isGuarantorList: string[];
    exchmarketList: string[];
    lastDtStart: string;
    lastDtEnd: string;
    carryDtStart: string;
    carryDtEnd: string;
    paymentDtStart: string;
    paymentDtEnd: string;
}

// 查询条件
const queryParams = ref<QueryParams>({
    listDateType: "",
    firstDtStart: "",
    firstDtEnd: "",
    compPropertyList: [],
    creditRatingList: [],
    districtCodeList: [],
    compindCodeList: [],
    bondRatingList: [],
    bondTypeList: [],
    termList: [],
    keyWords: "",
    cnbdCreditRatingList: [],
    productCodeList: [],
    isGuarantorList: [],
    exchmarketList: [],
    lastDtStart: "",
    lastDtEnd: "",
    carryDtStart: "",
    carryDtEnd: "",
    paymentDtStart: "",
    paymentDtEnd: ""
});

// 搜索关键词
const searchKeyword = ref('');

// 主体性质数据
const subjectPropertyList = ref<any[]>([]);


// 使用权限 store
const permissionStore = usePermissionStore();
const { getTabBarPermissions, getSysVersion } = storeToRefs(permissionStore);

// 控制tabbar显示/隐藏
const showTabBar = ref(true);

// 页面状态
const isLoading = ref(false);
const hasMore = ref(true);

// 分页参数
const pageSize = ref(20);
const pageNo = ref(1);

// 监听系统版本变化，当版本切换时重新获取数据
watch(getSysVersion, async (newVersion, oldVersion) => {
    // 当版本发生变化且不是初始化时，重新获取所有数据
    if (oldVersion && newVersion && oldVersion !== newVersion) {
        try {
            // 显示加载提示
            uni.showLoading({
                title: '切换版本中...'
            });

            // 重新获取所有配置数据
            await Promise.all([
                getBondTypeData(),      // 重新获取债券类型数据
                getPropertyData(),      // 重新获取主体性质数据
                getIndustryData()       // 重新获取行业数据
            ]);

            // 重新获取表头和SQL配置数据
            await getCustomListHeadData();

            // 重新初始化默认选择状态
            if (industryData.value.length > 0) {
                // 展开所有节点
                expandAll();
                // 全选所有行业
                selectedIndustries.value = getAllLevel4Codes();
                // 更新查询参数
                queryParams.value.compindCodeList = selectedIndustries.value;
            }

            // 重新初始化默认全选所有债券类型
            if (bondTypeOptions.value.length > 0) {
                selectAllBondTypes();
                queryParams.value.bondTypeList = selectedBondTypes.value;
            }

            // 重新初始化默认全选所有评级
            selectedSubjectRatings.value = [...subjectRatingOptions];
            selectedDebtRatings.value = [...debtRatingOptions];
            queryParams.value.creditRatingList = selectedSubjectRatings.value;
            queryParams.value.bondRatingList = selectedDebtRatings.value;

            // 最后重新查询发行数据
            await queryIssuanceData();

        } catch (error) {
            uni.showToast({
                title: '数据更新失败',
                icon: 'error'
            });
        } finally {
            // 隐藏加载提示
            uni.hideLoading();
        }
    }
}, { immediate: false }); // 不要立即执行，只监听变化

// 发行状态弹窗相关
const statusPopup = ref();
const currentStatus = ref(''); // 默认为空，与queryParams保持一致

// 发行状态选项
const statusOptions = [
    { label: '近期已发行', value: '近期已发行' },
    { label: '今日发行', value: '今日发行' },
    { label: '即将发行', value: '即将发行' },
    { label: '推迟发行', value: '推迟发行' },
    { label: '取消发行', value: '取消发行' }
];

// 债券类型弹窗相关
const bondTypePopup = ref();
const selectedBondTypes = ref<string[]>([]); // 选中的债券类型代码数组，默认为空
const bondTypeOptions = ref<any[]>([]); // 债券类型选项数据

// 主体评级弹窗相关
const subjectRatingPopup = ref();
const selectedSubjectRatings = ref<string[]>([]); // 选中的主体评级数组，默认为空
const subjectRatingOptions = [
    'AAA+', 'AAA', 'AAA-', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-', 'BBB+', 'BBB及以下', '其他', '无评级'
];

// 债项评级弹窗相关
const debtRatingPopup = ref();
const selectedDebtRatings = ref<string[]>([]); // 选中的债项评级数组
const debtRatingOptions = [
    'AAA+', 'AAA', 'AAA-', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-', '其他', '无评级'
];

// 主体性质弹窗相关
const subjectPropertyPopup = ref();
const selectedSubjectProperties = ref<string[]>([]); // 选中的主体性质itemcode数组，默认为空

// 过滤有效的主体性质列表（只显示effectflag为E的项）
const filteredSubjectPropertyList = computed(() => {
    return subjectPropertyList.value.filter(item => item.effectflag === 'E');
});

// 行业弹窗相关
const industryPopup = ref();
const industryData = ref<any[]>([]); // 行业数据
const selectedIndustries = ref<string[]>([]); // 选中的行业代码数组，默认为空

// 区域选择弹窗相关
const areaVisible = ref(false);
const selectedAreaIds = ref<string[]>([]); // 选中的区域id数组（仅区级），默认为空

// 添加一个key值用于强制更新组件
const areaSelectKey = ref(0);

// 监听区域选择器显示状态，控制tabbar
watch(areaVisible, (newValue) => {
    showTabBar.value = !newValue;
});

// 监听发行状态变化，自动调整日期范围
watch(currentStatus, (newStatus) => {
    const showDateStatuses = ['近期已发行', '推迟发行', '取消发行'];
    if (newStatus && !showDateStatuses.includes(newStatus)) {
        // 如果不是指定状态，自动设置日期为近三个月
        initializeDefaultDateRange();
    }
});

// 树形结构展开状态
const expandedLevel1 = ref<string[]>([]); // 展开的一级分类
const expandedLevel2 = ref<string[]>([]); // 展开的二级分类
const expandedLevel3 = ref<string[]>([]); // 展开的三级分类

// 发行起始日期选择相关
const dateRangeVisible = ref(false);
const selectedStartDateString = ref('');
const selectedEndDateString = ref('');

// 计算是否显示发行起始日期选择按钮
const shouldShowDateButton = computed(() => {
    const showDateStatuses = ['近期已发行', '推迟发行', '取消发行'];
    return showDateStatuses.includes(currentStatus.value);
});

// 初始化默认日期范围为近三个月
const initializeDefaultDateRange = () => {
    const now = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(now.getMonth() - 3);

    // 格式化日期为 YYYY-MM-DD
    const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    queryParams.value.firstDtStart = formatDate(threeMonthsAgo);
    queryParams.value.firstDtEnd = formatDate(now);
    selectedStartDateString.value = queryParams.value.firstDtStart;
    selectedEndDateString.value = queryParams.value.firstDtEnd;
};

// 初始化默认日期范围，与queryParams保持一致
const initializeDateRange = () => {
    if (!queryParams.value.firstDtStart || !queryParams.value.firstDtEnd) {
        initializeDefaultDateRange();
    } else {
        selectedStartDateString.value = queryParams.value.firstDtStart;
        selectedEndDateString.value = queryParams.value.firstDtEnd;
    }
};

// 计算默认日期对象，基于queryParams中的值
const defaultStartDate = computed(() => {
    if (queryParams.value.firstDtStart) {
        return new Date(queryParams.value.firstDtStart);
    }
    // 如果没有设置日期，返回当前日期的前三个月
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    return threeMonthsAgo;
});

const defaultEndDate = computed(() => {
    if (queryParams.value.firstDtEnd) {
        return new Date(queryParams.value.firstDtEnd);
    }
    // 如果没有设置日期，返回当前日期
    return new Date();
});

// 表格列定义 - 现在使用动态配置
const tableColumns = ref<any[]>([]);

// 债券列表数据 - 现在从接口获取，明确指定类型为any[]
const bondsList = ref<any[]>([]);

// 清除搜索
const clearSearch = () => {
    queryParams.value.keyWords = '';
    // 执行查询
    queryIssuanceData();
};

// 搜索函数（当用户输入完成后触发）
const handleSearch = () => {
    // 执行查询
    queryIssuanceData();
};

// 显示筛选选项
const showFilterOptions = (type: string) => {
    // 隐藏tabbar
    showTabBar.value = false;

    if (type === 'status') {
        statusPopup.value?.open();
    } else if (type === 'type') {
        bondTypePopup.value?.open();
    } else if (type === 'rating') {
        subjectRatingPopup.value?.open();
    } else if (type === 'debtRating') {
        debtRatingPopup.value?.open();
    } else if (type === 'property') {
        subjectPropertyPopup.value?.open();
    } else if (type === 'industry') {
        industryPopup.value?.open();
    } else if (type === 'area') {
        openAreaSelector();
    } else if (type === 'date') {
        dateRangeVisible.value = true;
    }
    // 其他筛选选项的弹出逻辑
};

// 发行状态弹窗相关方法
const closeStatusPicker = () => {
    statusPopup.value?.close();
    // 显示tabbar
    showTabBar.value = true;
};

const selectStatus = (value: string) => {
    currentStatus.value = value;
};

const confirmStatusSelection = () => {
    // 更新查询参数中的发行状态
    queryParams.value.listDateType = currentStatus.value;

    // 如果不是指定状态，自动设置日期为近三个月
    const showDateStatuses = ['近期已发行', '推迟发行', '取消发行'];
    if (!showDateStatuses.includes(currentStatus.value)) {
        initializeDefaultDateRange();
    }

    printQueryParams();
    closeStatusPicker();
    // 执行查询
    queryIssuanceData();
};

// 发行状态弹窗状态变化事件
const onStatusPopupChange = (e: any) => {
    if (e.show === false) {
        // 弹窗关闭时显示tabbar
        showTabBar.value = true;
    }
};


// 债券类型弹窗相关方法
const closeBondTypePicker = () => {
    bondTypePopup.value?.close();
    // 显示tabbar
    showTabBar.value = true;
};

const toggleBondType = (bondTypeCode: string) => {
    const index = selectedBondTypes.value.indexOf(bondTypeCode);
    if (index > -1) {
        selectedBondTypes.value.splice(index, 1);
    } else {
        selectedBondTypes.value.push(bondTypeCode);
    }
};

const selectAllBondTypes = () => {
    const allCodes: string[] = [];
    bondTypeOptions.value.forEach(category => {
        category.list.forEach(item => {
            allCodes.push(item.bondTypeCode);
        });
    });
    selectedBondTypes.value = allCodes;
};

const clearBondTypes = () => {
    selectedBondTypes.value = [];
};

const invertBondTypes = () => {
    const allCodes: string[] = [];
    bondTypeOptions.value.forEach(category => {
        category.list.forEach(item => {
            allCodes.push(item.bondTypeCode);
        });
    });
    selectedBondTypes.value = allCodes.filter(code => !selectedBondTypes.value.includes(code));
};

const confirmBondTypeSelection = () => {
    // 更新查询参数中的债券类型列表
    queryParams.value.bondTypeList = selectedBondTypes.value;
    printQueryParams();
    closeBondTypePicker();
    // 执行查询
    queryIssuanceData();
};

// 债券类型弹窗状态变化事件
const onBondTypePopupChange = (e: any) => {
    if (e.show === false) {
        // 弹窗关闭时显示tabbar
        showTabBar.value = true;
    }
};

// 主体评级弹窗相关方法
const closeSubjectRatingPicker = () => {
    subjectRatingPopup.value?.close();
    // 显示tabbar
    showTabBar.value = true;
};

const toggleSubjectRating = (rating: string) => {
    const index = selectedSubjectRatings.value.indexOf(rating);
    if (index > -1) {
        selectedSubjectRatings.value.splice(index, 1);
    } else {
        selectedSubjectRatings.value.push(rating);
    }
};

const selectAllSubjectRatings = () => {
    selectedSubjectRatings.value = [...subjectRatingOptions];
};

const clearSubjectRatings = () => {
    selectedSubjectRatings.value = [];
};

const invertSubjectRatings = () => {
    selectedSubjectRatings.value = subjectRatingOptions.filter(rating => !selectedSubjectRatings.value.includes(rating));
};

const confirmSubjectRatingSelection = () => {
    // 更新查询参数中的主体评级列表
    queryParams.value.creditRatingList = selectedSubjectRatings.value;
    printQueryParams();
    closeSubjectRatingPicker();
    // 执行查询
    queryIssuanceData();
};

// 主体评级弹窗状态变化事件
const onSubjectRatingPopupChange = (e: any) => {
    if (e.show === false) {
        // 弹窗关闭时显示tabbar
        showTabBar.value = true;
    }
};

// 债项评级弹窗相关方法
const closeDebtRatingPicker = () => {
    debtRatingPopup.value?.close();
    // 显示tabbar
    showTabBar.value = true;
};

const toggleDebtRating = (rating: string) => {
    const index = selectedDebtRatings.value.indexOf(rating);
    if (index > -1) {
        selectedDebtRatings.value.splice(index, 1);
    } else {
        selectedDebtRatings.value.push(rating);
    }
};

const selectAllDebtRatings = () => {
    selectedDebtRatings.value = [...debtRatingOptions];
};

const clearDebtRatings = () => {
    selectedDebtRatings.value = [];
};

const invertDebtRatings = () => {
    selectedDebtRatings.value = debtRatingOptions.filter(rating => !selectedDebtRatings.value.includes(rating));
};

const confirmDebtRatingSelection = () => {
    // 更新查询参数中的债项评级列表
    queryParams.value.bondRatingList = selectedDebtRatings.value;
    printQueryParams();
    closeDebtRatingPicker();
    // 执行查询
    queryIssuanceData();
};

// 债项评级弹窗状态变化事件
const onDebtRatingPopupChange = (e: any) => {
    if (e.show === false) {
        // 弹窗关闭时显示tabbar
        showTabBar.value = true;
    }
};

// 主体性质弹窗状态变化事件
const onSubjectPropertyPopupChange = (e: any) => {
    if (e.show === false) {
        // 弹窗关闭时显示tabbar
        showTabBar.value = true;
    }
};

// 行业弹窗状态变化事件
const onIndustryPopupChange = (e: any) => {
    if (e.show === false) {
        // 弹窗关闭时显示tabbar
        showTabBar.value = true;
    }
};



// 主体性质弹窗相关方法
const closeSubjectPropertyPicker = () => {
    subjectPropertyPopup.value?.close();
    // 显示tabbar
    showTabBar.value = true;
};

const toggleSubjectProperty = (itemcode: string) => {
    const index = selectedSubjectProperties.value.indexOf(itemcode);
    if (index > -1) {
        selectedSubjectProperties.value.splice(index, 1);
    } else {
        selectedSubjectProperties.value.push(itemcode);
    }
};

const confirmSubjectPropertySelection = () => {
    // 更新查询参数中的主体性质列表
    queryParams.value.compPropertyList = selectedSubjectProperties.value;
    printQueryParams();
    closeSubjectPropertyPicker();
    // 执行查询
    queryIssuanceData();
};

// 行业弹窗相关方法
const closeIndustryPicker = () => {
    industryPopup.value?.close();
    // 重置展开状态
    expandedLevel1.value = [];
    expandedLevel2.value = [];
    expandedLevel3.value = [];
    // 显示tabbar
    showTabBar.value = true;
};



// 切换行业选择
const toggleIndustry = (industryCode: string) => {
    const index = selectedIndustries.value.indexOf(industryCode);
    if (index > -1) {
        selectedIndustries.value.splice(index, 1);
    } else {
        selectedIndustries.value.push(industryCode);
    }
};

// 全选行业
const selectAllIndustries = () => {
    const allCodes = getAllLevel4Codes();
    selectedIndustries.value = allCodes;
};

// 清空行业选择
const clearIndustries = () => {
    selectedIndustries.value = [];
};

// 反选行业
const invertIndustries = () => {
    const allLevel4Codes = getAllLevel4Codes();
    selectedIndustries.value = allLevel4Codes.filter(code => !selectedIndustries.value.includes(code));
};

const confirmIndustrySelection = () => {
    // 更新查询参数中的行业选择列表
    queryParams.value.compindCodeList = selectedIndustries.value;
    printQueryParams();
    closeIndustryPicker();
    // 执行查询
    queryIssuanceData();
};

// 区域选择相关方法
const handleAreaChange = (selectedIds: string[]) => {
    // 只保存用户实际选择的值
    selectedAreaIds.value = selectedIds;
    // 更新查询参数中的区域代码列表
    queryParams.value.districtCodeList = selectedIds;
    // 执行查询
    queryIssuanceData();
};

// 区域选择器关闭事件
const onAreaClose = () => {
    // 区域选择器关闭时的处理
    showTabBar.value = true;
};

// 打开区域选择器
const openAreaSelector = () => {
    // 强制组件重新渲染，确保不会保留之前的选中状态
    areaSelectKey.value += 1;
    // 确保组件每次打开时都使用当前最新的选择状态
    areaVisible.value = true;
};

// 初始化默认区域为空
const initializeDefaultAreaSelection = async () => {
    // 重置为空数组
    selectedAreaIds.value = [];
    queryParams.value.districtCodeList = [];
    return [];
};

// 树形结构展开/收起方法
const toggleLevel1 = (code: string) => {
    const index = expandedLevel1.value.indexOf(code);
    if (index > -1) {
        expandedLevel1.value.splice(index, 1);
        // 收起时同时收起所有子级
        expandedLevel2.value = expandedLevel2.value.filter(item => {
            const level1Item = industryData.value.find(l1 => l1.sInfoCompindCode === code);
            return !level1Item?.list?.some(l2 => l2.sInfoCompindCode === item);
        });
        expandedLevel3.value = expandedLevel3.value.filter(item => {
            const level1Item = industryData.value.find(l1 => l1.sInfoCompindCode === code);
            return !level1Item?.list?.some(l2 => l2.list?.some(l3 => l3.sInfoCompindCode === item));
        });
    } else {
        expandedLevel1.value.push(code);
    }
};

const toggleLevel2 = (code: string) => {
    const index = expandedLevel2.value.indexOf(code);
    if (index > -1) {
        expandedLevel2.value.splice(index, 1);
        // 收起时同时收起所有子级
        expandedLevel3.value = expandedLevel3.value.filter(item => {
            // 找到对应的二级分类，检查是否包含该三级分类
            for (const level1 of industryData.value) {
                const level2Item = level1.list?.find(l2 => l2.sInfoCompindCode === code);
                if (level2Item?.list?.some(l3 => l3.sInfoCompindCode === item)) {
                    return false;
                }
            }
            return true;
        });
    } else {
        expandedLevel2.value.push(code);
    }
};

const toggleLevel3 = (code: string) => {
    const index = expandedLevel3.value.indexOf(code);
    if (index > -1) {
        expandedLevel3.value.splice(index, 1);
    } else {
        expandedLevel3.value.push(code);
    }
};

// 展开全部
const expandAll = () => {
    const level1Codes: string[] = [];
    const level2Codes: string[] = [];
    const level3Codes: string[] = [];

    industryData.value.forEach(level1 => {
        level1Codes.push(level1.sInfoCompindCode);
        level1.list?.forEach(level2 => {
            level2Codes.push(level2.sInfoCompindCode);
            level2.list?.forEach(level3 => {
                level3Codes.push(level3.sInfoCompindCode);
            });
        });
    });

    expandedLevel1.value = level1Codes;
    expandedLevel2.value = level2Codes;
    expandedLevel3.value = level3Codes;
};

// 收起全部
const collapseAll = () => {
    expandedLevel1.value = [];
    expandedLevel2.value = [];
    expandedLevel3.value = [];
};

// 获取所有四级分类代码
const getAllLevel4Codes = () => {
    const codes: string[] = [];
    const traverse = (items: any[]) => {
        items.forEach(item => {
            if (item.dataLevel === '4') {
                codes.push(item.sInfoCompindCode);
            } else if (item.list && item.list.length > 0) {
                traverse(item.list);
            }
        });
    };
    traverse(industryData.value);
    return codes;
};

// 获取指定分类下的所有四级分类代码
const getLevel4CodesUnder = (parentItem: any): string[] => {
    const codes: string[] = [];
    const traverse = (items: any[]) => {
        items.forEach(item => {
            if (item.dataLevel === '4') {
                codes.push(item.sInfoCompindCode);
            } else if (item.list && item.list.length > 0) {
                traverse(item.list);
            }
        });
    };
    if (parentItem.list) {
        traverse(parentItem.list);
    }
    return codes;
};

// 判断一级分类是否全选
const isLevel1Selected = (level1: any): boolean => {
    const level4Codes = getLevel4CodesUnder(level1);
    return level4Codes.length > 0 && level4Codes.every(code => selectedIndustries.value.includes(code));
};

// 判断一级分类是否半选
const isLevel1Indeterminate = (level1: any): boolean => {
    const level4Codes = getLevel4CodesUnder(level1);
    const selectedCount = level4Codes.filter(code => selectedIndustries.value.includes(code)).length;
    return selectedCount > 0 && selectedCount < level4Codes.length;
};

// 判断二级分类是否全选
const isLevel2Selected = (level2: any): boolean => {
    const level4Codes = getLevel4CodesUnder(level2);
    return level4Codes.length > 0 && level4Codes.every(code => selectedIndustries.value.includes(code));
};

// 判断二级分类是否半选
const isLevel2Indeterminate = (level2: any): boolean => {
    const level4Codes = getLevel4CodesUnder(level2);
    const selectedCount = level4Codes.filter(code => selectedIndustries.value.includes(code)).length;
    return selectedCount > 0 && selectedCount < level4Codes.length;
};

// 判断三级分类是否全选
const isLevel3Selected = (level3: any): boolean => {
    const level4Codes = getLevel4CodesUnder(level3);
    return level4Codes.length > 0 && level4Codes.every(code => selectedIndustries.value.includes(code));
};

// 判断三级分类是否半选
const isLevel3Indeterminate = (level3: any): boolean => {
    const level4Codes = getLevel4CodesUnder(level3);
    const selectedCount = level4Codes.filter(code => selectedIndustries.value.includes(code)).length;
    return selectedCount > 0 && selectedCount < level4Codes.length;
};

// 切换一级分类选择状态
const toggleLevel1Selection = (level1: any) => {
    const level4Codes = getLevel4CodesUnder(level1);
    const isSelected = isLevel1Selected(level1);

    if (isSelected) {
        // 取消选择所有下级
        selectedIndustries.value = selectedIndustries.value.filter(code => !level4Codes.includes(code));
    } else {
        // 选择所有下级
        level4Codes.forEach(code => {
            if (!selectedIndustries.value.includes(code)) {
                selectedIndustries.value.push(code);
            }
        });
    }
};

// 切换二级分类选择状态
const toggleLevel2Selection = (level2: any) => {
    const level4Codes = getLevel4CodesUnder(level2);
    const isSelected = isLevel2Selected(level2);

    if (isSelected) {
        // 取消选择所有下级
        selectedIndustries.value = selectedIndustries.value.filter(code => !level4Codes.includes(code));
    } else {
        // 选择所有下级
        level4Codes.forEach(code => {
            if (!selectedIndustries.value.includes(code)) {
                selectedIndustries.value.push(code);
            }
        });
    }
};

// 切换三级分类选择状态
const toggleLevel3Selection = (level3: any) => {
    const level4Codes = getLevel4CodesUnder(level3);
    const isSelected = isLevel3Selected(level3);

    if (isSelected) {
        // 取消选择所有下级
        selectedIndustries.value = selectedIndustries.value.filter(code => !level4Codes.includes(code));
    } else {
        // 选择所有下级
        level4Codes.forEach(code => {
            if (!selectedIndustries.value.includes(code)) {
                selectedIndustries.value.push(code);
            }
        });
    }
};

// 初始化默认状态（全部展开，全部选中）- 仅在需要重置时使用
const initializeDefaultState = () => {
    // 默认全部展开
    expandAll();
    // 默认全部选中
    selectedIndustries.value = getAllLevel4Codes();
};

// 发行起始日期选择相关方法
const updateDateRangeVisible = (visible: boolean) => {
    dateRangeVisible.value = visible;
    // 根据日期选择器的显示状态控制tabbar
    showTabBar.value = !visible;
};

const handleDateRangeChange = (dateRange: string[]) => {
    selectedStartDateString.value = dateRange[0];
    selectedEndDateString.value = dateRange[1];

    // 更新查询参数中的起始日期
    queryParams.value.firstDtStart = dateRange[0];
    queryParams.value.firstDtEnd = dateRange[1];
    printQueryParams();

    // 执行查询
    queryIssuanceData();
};



// 单元格点击显示债券详情
const showBondDetailFromCell = (row: any, column: any, rowIndex: number, colIndex: number) => {
    // 实现跳转到债券详情页的逻辑，和行点击一样
    uni.navigateTo({
        url: `/subPageA/bond-detail/index?objectId=${row.objectId}`,
    });
};

// 加载更多
const loadMore = async () => {
    if (isLoading.value || !hasMore.value) {
        return;
    }
    
    isLoading.value = true;
    pageNo.value += 1;
    
    try {
        const data = {
            page: {
                "pageNo": pageNo.value,
                "pageSize": pageSize.value,
                "sort": null,
                "direction": null
            },
            params: {
                ...queryParams.value,
                ccid: "b53fbde96c40413e90aa0347fc4890e4",
                ownedModuleid: "1369721787389640704"
            }
        }
        
        const res = await getSql(data);
        const newList = res.data.data.pageInfo.list;
        
        if (newList && newList.length > 0) {
            // 追加新数据到现有列表
            bondsList.value = [...bondsList.value, ...newList];
            
            // 如果返回的数据少于pageSize，说明没有更多数据了
            if (newList.length < pageSize.value) {
                hasMore.value = false;
            }
        } else {
            hasMore.value = false;
        }
    } catch (error) {
        pageNo.value -= 1; // 回滚页码
    } finally {
        isLoading.value = false;
    }
};

// 定义cellStyle函数
const cellStyle = ({ row, column, rowIndex, columnIndex }) => {
    // 首列特殊处理：宽度自适应，显示完整文字
    if (columnIndex === 0) {
        return {
            width: 'auto',
            minWidth: '200rpx',
            whiteSpace: 'normal',
            overflow: 'visible',
            textOverflow: 'unset',
            wordBreak: 'break-word',
            lineHeight: '1.4'
        };
    }
    return {};
};

// 定义表头样式
const headerCellStyle = ({ column, columnIndex }) => {
    return {
        fontWeight: 'bold',
        fontSize: '30rpx',
        backgroundColor: '#f6f8fa',
        color: '#333'
    };
};

// 获取债券类型数据
const getBondTypeData = async () => {
    try {
        const res = await getBondType({});
        if (res.data && res.data.data && res.data.data.length > 0) {
            bondTypeOptions.value = res.data.data;
            // 默认不选择任何项，保持空数组
        }
    } catch (error) {
        // 获取债券类型数据失败
    }
};
// 获取主体性质
const getPropertyData = async () => {
    const params = {
        "data": {
            clsno: "COMPPROPERTY",
            itemcode: "",
            cnname: "",
            effectflag: ""
        },
        "page": {
            "pageNo": 1,
            "pageSize": 20,
            "sort": "",
            "direction": "desc"
        }
    }
    try {
        const res = await getSubjectProperty(params);
        if (res.data && res.data.data && res.data.data.list.length > 0) {
            // 主体性质数据
            subjectPropertyList.value = res.data.data.list;
            // 保持默认选择，不覆盖queryParams中的初始值
        }
    } catch (error) {
        // 获取主体性质数据失败
    }
};
// 获取行业数据
const getIndustryData = async () => {
    try {
        const res = await getObtainData({});
        if (res.data && res.data.data && res.data.data.length > 0) {
            industryData.value = res.data.data;

            // 数据加载完成后展开所有节点
            expandAll();

            // 初始化默认选择全部行业
            initializeDefaultIndustrySelection();
        }
    } catch (error) {
        // 获取行业数据失败
    }
};

// 初始化默认选择全部行业
const initializeDefaultIndustrySelection = () => {
    // 确保已获取到行业数据
    if (industryData.value.length > 0) {
        selectedIndustries.value = getAllLevel4Codes();
        queryParams.value.compindCodeList = selectedIndustries.value;
    }
};
// 查询表头数据
const getCustomListHeadData = async () => {
    const data = {
        id: "b53fbde96c40413e90aa0347fc4890e4",
        moduleId: "1369721787389640704"
    }
    try {
        const res = await getCustomListHead(data);
        
        if (res.data && res.data.data && res.data.data.column) {
            const columnList = res.data.data.column;
            
            // 过滤出需要显示的列（defaultShow为'Y'的列）
            const visibleColumns = columnList
                .filter(col => col.defaultShow === 'Y')
                .sort((a, b) => a.order - b.order); // 按order字段排序
            
            // 动态生成表格列配置
            const dynamicColumns: any = visibleColumns.map((col, index) => {
                const columnConfig: any = {
                    name: col.columnCode,  // 字段名
                    label: col.columnTitle, // 显示名称
                    emptyString: '--'
                };
                
                // 根据数据类型设置对齐方式
                if (col.columnAlign) {
                    columnConfig.align = col.columnAlign;
                }
                
                // 如果是第一列（通常是债券简称），设置为固定列，但不限制宽度，让其自适应
                if (index === 0) {
                    columnConfig.fixed = true;
                    // 移除宽度和文字省略限制，让首列自适应宽度
                }
                // 其他列不设置width，让其自适应内容宽度
                
                return columnConfig;
            });
            
            // 更新表格列配置
            tableColumns.value = dynamicColumns;
        }
    } catch (error) {
        // 获取表头数据失败
    }
}
// 查询发行列表数据
const queryIssuanceData = async () => {
    // 重置分页状态
    pageNo.value = 1;
    hasMore.value = true;
    isLoading.value = true;
    
    const data = {
        page: {
            "pageNo": pageNo.value,
            "pageSize": pageSize.value,
            "sort": null,
            "direction": null
        },
        params: {
            ...queryParams.value,
            ccid: "b53fbde96c40413e90aa0347fc4890e4",
            ownedModuleid: "1369721787389640704"
        }
    }
    
    try {
        const res = await getSql(data);
        const list = res.data.data.pageInfo.list;
        bondsList.value = list || []; // 确保是数组类型
        
        // 检查是否还有更多数据
        if (!list || list.length < pageSize.value) {
            hasMore.value = false;
        }
    } catch (error) {
        bondsList.value = [];
        hasMore.value = false;
    } finally {
        isLoading.value = false;
    }
};


// 打印当前查询参数（调试用）
const printQueryParams = () => {
    // 移除打印语句
};

// 页面加载时获取数据
onMounted(async () => {
    // 初始化默认日期范围为近三个月
    initializeDefaultDateRange();

    try {
        // 确保区域数据为空
        selectedAreaIds.value = [];
        queryParams.value.districtCodeList = [];

        // 并行获取所有配置数据以提高加载速度
        const [bondTypePromise, propertyPromise, industryPromise, headerPromise] = [
            getBondTypeData(),
            getPropertyData(),
            getIndustryData(),
            getCustomListHeadData()  // 表头配置也并行加载
        ];

        // 等待所有配置数据加载完成
        await Promise.all([bondTypePromise, propertyPromise, industryPromise, headerPromise]);


        // 确保行业数据已加载完成后，初始化默认选择全部行业
        if (industryData.value.length > 0) {
            // 展开所有节点
            expandAll();
            // 全选所有行业
            selectedIndustries.value = getAllLevel4Codes();
            // 更新查询参数
            queryParams.value.compindCodeList = selectedIndustries.value;
        }

        // 初始化默认全选所有债券类型
        if (bondTypeOptions.value.length > 0) {
            selectAllBondTypes();
            queryParams.value.bondTypeList = selectedBondTypes.value;
        }

        // 初始化默认全选所有评级
        selectedSubjectRatings.value = [...subjectRatingOptions];
        selectedDebtRatings.value = [...debtRatingOptions];
        queryParams.value.creditRatingList = selectedSubjectRatings.value;
        queryParams.value.bondRatingList = selectedDebtRatings.value;

        // 在所有配置数据都加载完成后执行查询
        queryIssuanceData();
    } catch (error) {
        // 页面初始化失败
        // 确保错误处理中也设置区域为空
        selectedAreaIds.value = [];
        queryParams.value.districtCodeList = [];
        // 出错时也尝试执行查询
        queryIssuanceData();
    }
});

// 页面显示时获取权限数据
onShow(() => {
    // 获取TabBar权限列表
});
</script>

<style lang="scss" scoped>
/* 1.1 页面容器 */
.container {
    padding: 0 20rpx;
    height: 100vh;
    box-sizing: border-box;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    /* 防止整体页面滚动 */
}

/* 1.2 头部区域 */
.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* 1.3 内容区域 */
.content-wrapper {
    flex: 1;
    margin-top: 180rpx;
    overflow: auto;
    /* 主滚动容器 */
    position: relative;
}

/* 1.4 滚动区域 */
.scrollable-content {
    height: 100%;

    ::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
        color: transparent;
    }
}

/* 搜索框 */
.search-box {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
}

.search-input {
    flex: 1;
    height: 72rpx;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);

    uni-icons {
        margin-right: 10rpx;
    }

    input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
    }

    .placeholder {
        color: #999999;
    }
}

.cancel-btn {
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #ff9500;
}

/* 卡片容器 */
.card-container {
    background-color: #ffffff;
    border-radius: 20rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 筛选条件 */


.filter-row {
    display: flex;
    white-space: nowrap;
    margin-bottom: 20rpx;
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-around;
}

.filter-row::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
}

.filter-item {
    display: inline-flex;
    align-items: center;
    padding: 10rpx 0;
    border-radius: 30rpx;
    font-size: 26rpx;
    justify-content: center;

}

.filter-item.secondary {
    background-color: #f0f0f0;
    padding: 10rpx 20rpx;
    margin-right: 20rpx;
}

.arrow {
    margin-left: 8rpx;
    font-size: 20rpx;
    color: #696969;
}

/* 债券列表 */
.bonds-list {
    padding: 10rpx 0;
    position: relative;
    /* 移除固定高度，让SimpleTable组件自己控制高度 */
}

/* 弹窗样式 */
.popup-content {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 30rpx 30rpx 0;
    max-height: 70vh;
    display: flex;
    flex-direction: column;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;
    border-bottom: 2rpx solid #F5F5F5;

    .popup-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }

    .close-icon {
        padding: 10rpx;
    }
}

.popup-options {
    flex: 1;
    overflow: hidden;
    padding: 20rpx 0;
    max-height: 50vh;

    /* 状态选择样式 */
    .status-options-list {
        padding: 20rpx 0;
        max-height: 40vh;
        overflow-y: auto;

        /* 隐藏滚动条 - 兼容各端 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */

        &::-webkit-scrollbar {
            display: none; /* Chrome Safari */
            width: 0;
            height: 0;
            background: transparent;
        }

        .status-option-item {
            display: flex;
            align-items: center;
            padding: 30rpx 0;

            .radio {
                width: 36rpx;
                height: 36rpx;
                border-radius: 50%;
                border: 2rpx solid #DCDFE6;
                margin-right: 20rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s;

                &.checked {
                    border-color: #FF9900;
                }

                .radio-inner {
                    width: 20rpx;
                    height: 20rpx;
                    border-radius: 50%;
                    background-color: #FF9900;
                }
            }

            .option-label {
                font-size: 28rpx;
                color: #333;
            }
        }
    }

    /* 债券类型选择样式 */
    .options-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;

        text {
            font-size: 26rpx;
            color: #999;
        }

        .options-actions {
            display: flex;
            gap: 30rpx;

            text {
                color: #FF9900;
                font-size: 26rpx;

                &:active {
                    opacity: 0.8;
                }
            }
        }
    }

    .options-list {
        padding: 20rpx 0;
        max-height: 40vh;
        overflow-y: auto;

        /* 隐藏滚动条 - 兼容各端 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE 10+ */

        &::-webkit-scrollbar {
            display: none; /* Chrome Safari */
            width: 0;
            height: 0;
            background: transparent;
        }

        .category-title {
            font-size: 30rpx;
            font-weight: bold;
            color: #333;
            padding: 20rpx 0 10rpx;
            border-bottom: 1rpx solid #f0f0f0;
            margin-bottom: 10rpx;
        }

        .sub-category-title {
            font-size: 28rpx;
            font-weight: 600;
            color: #666;
            padding: 15rpx 0 8rpx 20rpx;
            background-color: #f8f8f8;
            margin-bottom: 8rpx;
        }

        .sub-sub-category-title {
            font-size: 26rpx;
            font-weight: 500;
            color: #888;
            padding: 10rpx 0 5rpx 40rpx;
            background-color: #fafafa;
            margin-bottom: 5rpx;
        }

        .category-options {
            display: flex;
            flex-wrap: wrap;
        }

        .option-item {
            display: flex;
            align-items: center;
            padding: 20rpx 0;
            width: 50%;
            flex-shrink: 0;

            .checkbox {
                width: 36rpx;
                height: 36rpx;
                border-radius: 6rpx;
                border: 2rpx solid #DCDFE6;
                margin-right: 20rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s;

                &.checked {
                    background-color: #FF9900;
                    border-color: #FF9900;
                }
            }

            .option-label {
                font-size: 28rpx;
                color: #333;
                flex: 1;
            }
        }
    }
}

.popup-footer {
    padding: 30rpx;
    border-top: 2rpx solid #F5F5F5;

    .confirm-btn {
        width: 100%;
        height: 88rpx;
        background: #FF9900;
        border-radius: 44rpx;
        color: #FFFFFF;
        font-size: 32rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;

        &:active {
            opacity: 0.9;
        }
    }
}

/* 树形结构样式 */
.tree-container {
    height: 400rpx;

    .tree-level {
        .tree-item {
            padding: 15rpx 0;
            transition: all 0.3s;

            .tree-content {
                display: flex;
                align-items: center;

                .checkbox {
                    width: 32rpx;
                    height: 32rpx;
                    border-radius: 6rpx;
                    border: 2rpx solid #DCDFE6;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s;
                    flex-shrink: 0;
                    margin-right: 15rpx;

                    &.checked {
                        background-color: #FF9900;
                        border-color: #FF9900;
                    }

                    &.indeterminate {
                        background-color: #FF9900;
                        border-color: #FF9900;
                    }

                    .indeterminate-icon {
                        width: 16rpx;
                        height: 3rpx;
                        background-color: #fff;
                        border-radius: 2rpx;
                    }
                }

                .tree-label {
                    flex: 1;
                    font-size: 28rpx;
                    color: #333;
                    word-break: break-all;
                }
            }

            &.level-1 {
                .tree-label {
                    font-weight: bold;
                    font-size: 30rpx;
                }
            }

            &.level-2 {
                padding-left: 40rpx;

                .tree-label {
                    font-weight: 600;
                    font-size: 28rpx;
                    color: #555;
                }
            }

            &.level-3 {
                padding-left: 80rpx;

                .tree-label {
                    font-size: 26rpx;
                    color: #666;
                }
            }

            &.level-4 {
                padding-left: 120rpx;

                .tree-label {
                    font-size: 26rpx;
                    color: #777;
                }

                &.selectable {
                    &:active {
                        background-color: #f8f8f8;
                    }
                }
            }
        }
    }
}

/* 无数据提示样式 */
.no-data-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 0 60rpx 0;
    .no-data-text {
        font-size: 28rpx;
        color: #999999;
    }
}

/* 没有更多数据提示 */
.no-more-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 0;
    
    .no-more-text {
        font-size: 24rpx;
        color: #c0c4cc;
        position: relative;
        
        &::before,
        &::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 60rpx;
            height: 1rpx;
            background-color: #e4e7ed;
        }
        
        &::before {
            left: -80rpx;
        }
        
        &::after {
            right: -80rpx;
        }
    }
}
</style>